# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
DEBUG=True

# Database Configuration
DATABASE_URL=sqlite:///app.db
DEV_DATABASE_URL=sqlite:///app_dev.db

# Cloud Storage Configuration
USE_CLOUD_STORAGE=False
CLOUD_PROVIDER=local  # Options: aws, gcp, azure, local

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket-name
AWS_REGION=us-east-1

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GOOGLE_CLOUD_BUCKET=your-gcs-bucket-name
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Azure Configuration
AZURE_STORAGE_CONNECTION_STRING=your-azure-connection-string
AZURE_CONTAINER_NAME=your-azure-container

# Redis Configuration (for caching and task queue)
REDIS_URL=redis://localhost:6379/0

# Celery Configuration (for background tasks)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=*********  # 100MB in bytes

# Classification Configuration
CLASSIFICATION_MODEL_PATH=models/classifier.pkl

# Web Scraping Configuration
SCRAPING_USER_AGENT=DocumentAnalytics/1.0 (+https://your-domain.com/bot)
SCRAPING_DELAY=1
SCRAPING_MAX_PAGES=100

# Search Configuration
SEARCH_RESULTS_PER_PAGE=20
SEARCH_HIGHLIGHT_CONTEXT=100

# Analytics Configuration
ANALYTICS_RETENTION_DAYS=90

# Security Configuration
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_EXPIRES=3600

# API Rate Limiting
RATELIMIT_STORAGE_URL=redis://localhost:6379/0
RATELIMIT_DEFAULT=100 per hour

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Development/Production Flags
ENABLE_CORS=True
ENABLE_DEBUG_TOOLBAR=False

# External API Keys (if needed)
OPENAI_API_KEY=your-openai-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key

# Monitoring and Health Checks
HEALTH_CHECK_ENABLED=True
METRICS_ENABLED=True

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# Deployment Configuration
PORT=5000
HOST=0.0.0.0
WORKERS=4
