function _y(c,o){for(var d=0;d<o.length;d++){const s=o[d];if(typeof s!="string"&&!Array.isArray(s)){for(const h in s)if(h!=="default"&&!(h in c)){const T=Object.getOwnPropertyDescriptor(s,h);T&&Object.defineProperty(c,h,T.get?T:{enumerable:!0,get:()=>s[h]})}}}return Object.freeze(Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}))}(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const h of document.querySelectorAll('link[rel="modulepreload"]'))s(h);new MutationObserver(h=>{for(const T of h)if(T.type==="childList")for(const M of T.addedNodes)M.tagName==="LINK"&&M.rel==="modulepreload"&&s(M)}).observe(document,{childList:!0,subtree:!0});function d(h){const T={};return h.integrity&&(T.integrity=h.integrity),h.referrerPolicy&&(T.referrerPolicy=h.referrerPolicy),h.crossOrigin==="use-credentials"?T.credentials="include":h.crossOrigin==="anonymous"?T.credentials="omit":T.credentials="same-origin",T}function s(h){if(h.ep)return;h.ep=!0;const T=d(h);fetch(h.href,T)}})();function My(c){return c&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")?c.default:c}var Of={exports:{}},Gn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vd;function zy(){if(Vd)return Gn;Vd=1;var c=Symbol.for("react.transitional.element"),o=Symbol.for("react.fragment");function d(s,h,T){var M=null;if(T!==void 0&&(M=""+T),h.key!==void 0&&(M=""+h.key),"key"in h){T={};for(var R in h)R!=="key"&&(T[R]=h[R])}else T=h;return h=T.ref,{$$typeof:c,type:s,key:M,ref:h!==void 0?h:null,props:T}}return Gn.Fragment=o,Gn.jsx=d,Gn.jsxs=d,Gn}var Qd;function Ny(){return Qd||(Qd=1,Of.exports=zy()),Of.exports}var S=Ny(),Rf={exports:{}},at={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ld;function Oy(){if(Ld)return at;Ld=1;var c=Symbol.for("react.transitional.element"),o=Symbol.for("react.portal"),d=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),h=Symbol.for("react.profiler"),T=Symbol.for("react.consumer"),M=Symbol.for("react.context"),R=Symbol.for("react.forward_ref"),E=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),H=Symbol.iterator;function Y(m){return m===null||typeof m!="object"?null:(m=H&&m[H]||m["@@iterator"],typeof m=="function"?m:null)}var tt={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},F=Object.assign,$={};function K(m,U,X){this.props=m,this.context=U,this.refs=$,this.updater=X||tt}K.prototype.isReactComponent={},K.prototype.setState=function(m,U){if(typeof m!="object"&&typeof m!="function"&&m!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,m,U,"setState")},K.prototype.forceUpdate=function(m){this.updater.enqueueForceUpdate(this,m,"forceUpdate")};function ct(){}ct.prototype=K.prototype;function gt(m,U,X){this.props=m,this.context=U,this.refs=$,this.updater=X||tt}var et=gt.prototype=new ct;et.constructor=gt,F(et,K.prototype),et.isPureReactComponent=!0;var ft=Array.isArray,j={H:null,A:null,T:null,S:null,V:null},lt=Object.prototype.hasOwnProperty;function bt(m,U,X,w,V,st){return X=st.ref,{$$typeof:c,type:m,key:U,ref:X!==void 0?X:null,props:st}}function G(m,U){return bt(m.type,U,void 0,void 0,void 0,m.props)}function Nt(m){return typeof m=="object"&&m!==null&&m.$$typeof===c}function he(m){var U={"=":"=0",":":"=2"};return"$"+m.replace(/[=:]/g,function(X){return U[X]})}var Jt=/\/+/g;function Rt(m,U){return typeof m=="object"&&m!==null&&m.key!=null?he(""+m.key):U.toString(36)}function ye(){}function ge(m){switch(m.status){case"fulfilled":return m.value;case"rejected":throw m.reason;default:switch(typeof m.status=="string"?m.then(ye,ye):(m.status="pending",m.then(function(U){m.status==="pending"&&(m.status="fulfilled",m.value=U)},function(U){m.status==="pending"&&(m.status="rejected",m.reason=U)})),m.status){case"fulfilled":return m.value;case"rejected":throw m.reason}}throw m}function zt(m,U,X,w,V){var st=typeof m;(st==="undefined"||st==="boolean")&&(m=null);var P=!1;if(m===null)P=!0;else switch(st){case"bigint":case"string":case"number":P=!0;break;case"object":switch(m.$$typeof){case c:case o:P=!0;break;case z:return P=m._init,zt(P(m._payload),U,X,w,V)}}if(P)return V=V(m),P=w===""?"."+Rt(m,0):w,ft(V)?(X="",P!=null&&(X=P.replace(Jt,"$&/")+"/"),zt(V,U,X,"",function(ue){return ue})):V!=null&&(Nt(V)&&(V=G(V,X+(V.key==null||m&&m.key===V.key?"":(""+V.key).replace(Jt,"$&/")+"/")+P)),U.push(V)),1;P=0;var mt=w===""?".":w+":";if(ft(m))for(var Et=0;Et<m.length;Et++)w=m[Et],st=mt+Rt(w,Et),P+=zt(w,U,X,st,V);else if(Et=Y(m),typeof Et=="function")for(m=Et.call(m),Et=0;!(w=m.next()).done;)w=w.value,st=mt+Rt(w,Et++),P+=zt(w,U,X,st,V);else if(st==="object"){if(typeof m.then=="function")return zt(ge(m),U,X,w,V);throw U=String(m),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(m).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return P}function _(m,U,X){if(m==null)return m;var w=[],V=0;return zt(m,w,"","",function(st){return U.call(X,st,V++)}),w}function B(m){if(m._status===-1){var U=m._result;U=U(),U.then(function(X){(m._status===0||m._status===-1)&&(m._status=1,m._result=X)},function(X){(m._status===0||m._status===-1)&&(m._status=2,m._result=X)}),m._status===-1&&(m._status=0,m._result=U)}if(m._status===1)return m._result.default;throw m._result}var C=typeof reportError=="function"?reportError:function(m){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof m=="object"&&m!==null&&typeof m.message=="string"?String(m.message):String(m),error:m});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",m);return}console.error(m)};function yt(){}return at.Children={map:_,forEach:function(m,U,X){_(m,function(){U.apply(this,arguments)},X)},count:function(m){var U=0;return _(m,function(){U++}),U},toArray:function(m){return _(m,function(U){return U})||[]},only:function(m){if(!Nt(m))throw Error("React.Children.only expected to receive a single React element child.");return m}},at.Component=K,at.Fragment=d,at.Profiler=h,at.PureComponent=gt,at.StrictMode=s,at.Suspense=E,at.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=j,at.__COMPILER_RUNTIME={__proto__:null,c:function(m){return j.H.useMemoCache(m)}},at.cache=function(m){return function(){return m.apply(null,arguments)}},at.cloneElement=function(m,U,X){if(m==null)throw Error("The argument must be a React element, but you passed "+m+".");var w=F({},m.props),V=m.key,st=void 0;if(U!=null)for(P in U.ref!==void 0&&(st=void 0),U.key!==void 0&&(V=""+U.key),U)!lt.call(U,P)||P==="key"||P==="__self"||P==="__source"||P==="ref"&&U.ref===void 0||(w[P]=U[P]);var P=arguments.length-2;if(P===1)w.children=X;else if(1<P){for(var mt=Array(P),Et=0;Et<P;Et++)mt[Et]=arguments[Et+2];w.children=mt}return bt(m.type,V,void 0,void 0,st,w)},at.createContext=function(m){return m={$$typeof:M,_currentValue:m,_currentValue2:m,_threadCount:0,Provider:null,Consumer:null},m.Provider=m,m.Consumer={$$typeof:T,_context:m},m},at.createElement=function(m,U,X){var w,V={},st=null;if(U!=null)for(w in U.key!==void 0&&(st=""+U.key),U)lt.call(U,w)&&w!=="key"&&w!=="__self"&&w!=="__source"&&(V[w]=U[w]);var P=arguments.length-2;if(P===1)V.children=X;else if(1<P){for(var mt=Array(P),Et=0;Et<P;Et++)mt[Et]=arguments[Et+2];V.children=mt}if(m&&m.defaultProps)for(w in P=m.defaultProps,P)V[w]===void 0&&(V[w]=P[w]);return bt(m,st,void 0,void 0,null,V)},at.createRef=function(){return{current:null}},at.forwardRef=function(m){return{$$typeof:R,render:m}},at.isValidElement=Nt,at.lazy=function(m){return{$$typeof:z,_payload:{_status:-1,_result:m},_init:B}},at.memo=function(m,U){return{$$typeof:g,type:m,compare:U===void 0?null:U}},at.startTransition=function(m){var U=j.T,X={};j.T=X;try{var w=m(),V=j.S;V!==null&&V(X,w),typeof w=="object"&&w!==null&&typeof w.then=="function"&&w.then(yt,C)}catch(st){C(st)}finally{j.T=U}},at.unstable_useCacheRefresh=function(){return j.H.useCacheRefresh()},at.use=function(m){return j.H.use(m)},at.useActionState=function(m,U,X){return j.H.useActionState(m,U,X)},at.useCallback=function(m,U){return j.H.useCallback(m,U)},at.useContext=function(m){return j.H.useContext(m)},at.useDebugValue=function(){},at.useDeferredValue=function(m,U){return j.H.useDeferredValue(m,U)},at.useEffect=function(m,U,X){var w=j.H;if(typeof X=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return w.useEffect(m,U)},at.useId=function(){return j.H.useId()},at.useImperativeHandle=function(m,U,X){return j.H.useImperativeHandle(m,U,X)},at.useInsertionEffect=function(m,U){return j.H.useInsertionEffect(m,U)},at.useLayoutEffect=function(m,U){return j.H.useLayoutEffect(m,U)},at.useMemo=function(m,U){return j.H.useMemo(m,U)},at.useOptimistic=function(m,U){return j.H.useOptimistic(m,U)},at.useReducer=function(m,U,X){return j.H.useReducer(m,U,X)},at.useRef=function(m){return j.H.useRef(m)},at.useState=function(m){return j.H.useState(m)},at.useSyncExternalStore=function(m,U,X){return j.H.useSyncExternalStore(m,U,X)},at.useTransition=function(){return j.H.useTransition()},at.version="19.1.0",at}var Zd;function Lf(){return Zd||(Zd=1,Rf.exports=Oy()),Rf.exports}var q=Lf();const Rl=My(q),hm=_y({__proto__:null,default:Rl},[q]);var Df={exports:{}},Yn={},Uf={exports:{}},jf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kd;function Ry(){return Kd||(Kd=1,function(c){function o(_,B){var C=_.length;_.push(B);t:for(;0<C;){var yt=C-1>>>1,m=_[yt];if(0<h(m,B))_[yt]=B,_[C]=m,C=yt;else break t}}function d(_){return _.length===0?null:_[0]}function s(_){if(_.length===0)return null;var B=_[0],C=_.pop();if(C!==B){_[0]=C;t:for(var yt=0,m=_.length,U=m>>>1;yt<U;){var X=2*(yt+1)-1,w=_[X],V=X+1,st=_[V];if(0>h(w,C))V<m&&0>h(st,w)?(_[yt]=st,_[V]=C,yt=V):(_[yt]=w,_[X]=C,yt=X);else if(V<m&&0>h(st,C))_[yt]=st,_[V]=C,yt=V;else break t}}return B}function h(_,B){var C=_.sortIndex-B.sortIndex;return C!==0?C:_.id-B.id}if(c.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var T=performance;c.unstable_now=function(){return T.now()}}else{var M=Date,R=M.now();c.unstable_now=function(){return M.now()-R}}var E=[],g=[],z=1,H=null,Y=3,tt=!1,F=!1,$=!1,K=!1,ct=typeof setTimeout=="function"?setTimeout:null,gt=typeof clearTimeout=="function"?clearTimeout:null,et=typeof setImmediate<"u"?setImmediate:null;function ft(_){for(var B=d(g);B!==null;){if(B.callback===null)s(g);else if(B.startTime<=_)s(g),B.sortIndex=B.expirationTime,o(E,B);else break;B=d(g)}}function j(_){if($=!1,ft(_),!F)if(d(E)!==null)F=!0,lt||(lt=!0,Rt());else{var B=d(g);B!==null&&zt(j,B.startTime-_)}}var lt=!1,bt=-1,G=5,Nt=-1;function he(){return K?!0:!(c.unstable_now()-Nt<G)}function Jt(){if(K=!1,lt){var _=c.unstable_now();Nt=_;var B=!0;try{t:{F=!1,$&&($=!1,gt(bt),bt=-1),tt=!0;var C=Y;try{e:{for(ft(_),H=d(E);H!==null&&!(H.expirationTime>_&&he());){var yt=H.callback;if(typeof yt=="function"){H.callback=null,Y=H.priorityLevel;var m=yt(H.expirationTime<=_);if(_=c.unstable_now(),typeof m=="function"){H.callback=m,ft(_),B=!0;break e}H===d(E)&&s(E),ft(_)}else s(E);H=d(E)}if(H!==null)B=!0;else{var U=d(g);U!==null&&zt(j,U.startTime-_),B=!1}}break t}finally{H=null,Y=C,tt=!1}B=void 0}}finally{B?Rt():lt=!1}}}var Rt;if(typeof et=="function")Rt=function(){et(Jt)};else if(typeof MessageChannel<"u"){var ye=new MessageChannel,ge=ye.port2;ye.port1.onmessage=Jt,Rt=function(){ge.postMessage(null)}}else Rt=function(){ct(Jt,0)};function zt(_,B){bt=ct(function(){_(c.unstable_now())},B)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(_){_.callback=null},c.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<_?Math.floor(1e3/_):5},c.unstable_getCurrentPriorityLevel=function(){return Y},c.unstable_next=function(_){switch(Y){case 1:case 2:case 3:var B=3;break;default:B=Y}var C=Y;Y=B;try{return _()}finally{Y=C}},c.unstable_requestPaint=function(){K=!0},c.unstable_runWithPriority=function(_,B){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var C=Y;Y=_;try{return B()}finally{Y=C}},c.unstable_scheduleCallback=function(_,B,C){var yt=c.unstable_now();switch(typeof C=="object"&&C!==null?(C=C.delay,C=typeof C=="number"&&0<C?yt+C:yt):C=yt,_){case 1:var m=-1;break;case 2:m=250;break;case 5:m=1073741823;break;case 4:m=1e4;break;default:m=5e3}return m=C+m,_={id:z++,callback:B,priorityLevel:_,startTime:C,expirationTime:m,sortIndex:-1},C>yt?(_.sortIndex=C,o(g,_),d(E)===null&&_===d(g)&&($?(gt(bt),bt=-1):$=!0,zt(j,C-yt))):(_.sortIndex=m,o(E,_),F||tt||(F=!0,lt||(lt=!0,Rt()))),_},c.unstable_shouldYield=he,c.unstable_wrapCallback=function(_){var B=Y;return function(){var C=Y;Y=B;try{return _.apply(this,arguments)}finally{Y=C}}}}(jf)),jf}var kd;function Dy(){return kd||(kd=1,Uf.exports=Ry()),Uf.exports}var Cf={exports:{}},Ft={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jd;function Uy(){if(Jd)return Ft;Jd=1;var c=Lf();function o(E){var g="https://react.dev/errors/"+E;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var z=2;z<arguments.length;z++)g+="&args[]="+encodeURIComponent(arguments[z])}return"Minified React error #"+E+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(){}var s={d:{f:d,r:function(){throw Error(o(522))},D:d,C:d,L:d,m:d,X:d,S:d,M:d},p:0,findDOMNode:null},h=Symbol.for("react.portal");function T(E,g,z){var H=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:h,key:H==null?null:""+H,children:E,containerInfo:g,implementation:z}}var M=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function R(E,g){if(E==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return Ft.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,Ft.createPortal=function(E,g){var z=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(o(299));return T(E,g,null,z)},Ft.flushSync=function(E){var g=M.T,z=s.p;try{if(M.T=null,s.p=2,E)return E()}finally{M.T=g,s.p=z,s.d.f()}},Ft.preconnect=function(E,g){typeof E=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,s.d.C(E,g))},Ft.prefetchDNS=function(E){typeof E=="string"&&s.d.D(E)},Ft.preinit=function(E,g){if(typeof E=="string"&&g&&typeof g.as=="string"){var z=g.as,H=R(z,g.crossOrigin),Y=typeof g.integrity=="string"?g.integrity:void 0,tt=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;z==="style"?s.d.S(E,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:H,integrity:Y,fetchPriority:tt}):z==="script"&&s.d.X(E,{crossOrigin:H,integrity:Y,fetchPriority:tt,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},Ft.preinitModule=function(E,g){if(typeof E=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var z=R(g.as,g.crossOrigin);s.d.M(E,{crossOrigin:z,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&s.d.M(E)},Ft.preload=function(E,g){if(typeof E=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var z=g.as,H=R(z,g.crossOrigin);s.d.L(E,z,{crossOrigin:H,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},Ft.preloadModule=function(E,g){if(typeof E=="string")if(g){var z=R(g.as,g.crossOrigin);s.d.m(E,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:z,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else s.d.m(E)},Ft.requestFormReset=function(E){s.d.r(E)},Ft.unstable_batchedUpdates=function(E,g){return E(g)},Ft.useFormState=function(E,g,z){return M.H.useFormState(E,g,z)},Ft.useFormStatus=function(){return M.H.useHostTransitionStatus()},Ft.version="19.1.0",Ft}var $d;function ym(){if($d)return Cf.exports;$d=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(o){console.error(o)}}return c(),Cf.exports=Uy(),Cf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wd;function jy(){if(Wd)return Yn;Wd=1;var c=Dy(),o=Lf(),d=ym();function s(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function h(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function T(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function M(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function R(t){if(T(t)!==t)throw Error(s(188))}function E(t){var e=t.alternate;if(!e){if(e=T(t),e===null)throw Error(s(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return R(n),t;if(u===a)return R(n),e;u=u.sibling}throw Error(s(188))}if(l.return!==a.return)l=n,a=u;else{for(var i=!1,f=n.child;f;){if(f===l){i=!0,l=n,a=u;break}if(f===a){i=!0,a=n,l=u;break}f=f.sibling}if(!i){for(f=u.child;f;){if(f===l){i=!0,l=u,a=n;break}if(f===a){i=!0,a=u,l=n;break}f=f.sibling}if(!i)throw Error(s(189))}}if(l.alternate!==a)throw Error(s(190))}if(l.tag!==3)throw Error(s(188));return l.stateNode.current===l?t:e}function g(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=g(t),e!==null)return e;t=t.sibling}return null}var z=Object.assign,H=Symbol.for("react.element"),Y=Symbol.for("react.transitional.element"),tt=Symbol.for("react.portal"),F=Symbol.for("react.fragment"),$=Symbol.for("react.strict_mode"),K=Symbol.for("react.profiler"),ct=Symbol.for("react.provider"),gt=Symbol.for("react.consumer"),et=Symbol.for("react.context"),ft=Symbol.for("react.forward_ref"),j=Symbol.for("react.suspense"),lt=Symbol.for("react.suspense_list"),bt=Symbol.for("react.memo"),G=Symbol.for("react.lazy"),Nt=Symbol.for("react.activity"),he=Symbol.for("react.memo_cache_sentinel"),Jt=Symbol.iterator;function Rt(t){return t===null||typeof t!="object"?null:(t=Jt&&t[Jt]||t["@@iterator"],typeof t=="function"?t:null)}var ye=Symbol.for("react.client.reference");function ge(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ye?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case F:return"Fragment";case K:return"Profiler";case $:return"StrictMode";case j:return"Suspense";case lt:return"SuspenseList";case Nt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case tt:return"Portal";case et:return(t.displayName||"Context")+".Provider";case gt:return(t._context.displayName||"Context")+".Consumer";case ft:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case bt:return e=t.displayName||null,e!==null?e:ge(t.type)||"Memo";case G:e=t._payload,t=t._init;try{return ge(t(e))}catch{}}return null}var zt=Array.isArray,_=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,B=d.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,C={pending:!1,data:null,method:null,action:null},yt=[],m=-1;function U(t){return{current:t}}function X(t){0>m||(t.current=yt[m],yt[m]=null,m--)}function w(t,e){m++,yt[m]=t.current,t.current=e}var V=U(null),st=U(null),P=U(null),mt=U(null);function Et(t,e){switch(w(P,e),w(st,t),w(V,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?yd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=yd(e),t=gd(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}X(V),w(V,t)}function ue(){X(V),X(st),X(P)}function ll(t){t.memoizedState!==null&&w(mt,t);var e=V.current,l=gd(e,t.type);e!==l&&(w(st,t),w(V,l))}function al(t){st.current===t&&(X(V),X(st)),mt.current===t&&(X(mt),Cn._currentValue=C)}var nl=Object.prototype.hasOwnProperty,hi=c.unstable_scheduleCallback,yi=c.unstable_cancelCallback,av=c.unstable_shouldYield,nv=c.unstable_requestPaint,Re=c.unstable_now,uv=c.unstable_getCurrentPriorityLevel,$f=c.unstable_ImmediatePriority,Wf=c.unstable_UserBlockingPriority,Zn=c.unstable_NormalPriority,iv=c.unstable_LowPriority,Ff=c.unstable_IdlePriority,cv=c.log,fv=c.unstable_setDisableYieldValue,Xa=null,ie=null;function ul(t){if(typeof cv=="function"&&fv(t),ie&&typeof ie.setStrictMode=="function")try{ie.setStrictMode(Xa,t)}catch{}}var ce=Math.clz32?Math.clz32:rv,sv=Math.log,ov=Math.LN2;function rv(t){return t>>>=0,t===0?32:31-(sv(t)/ov|0)|0}var Kn=256,kn=4194304;function Dl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Jn(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,u=t.suspendedLanes,i=t.pingedLanes;t=t.warmLanes;var f=a&134217727;return f!==0?(a=f&~u,a!==0?n=Dl(a):(i&=f,i!==0?n=Dl(i):l||(l=f&~t,l!==0&&(n=Dl(l))))):(f=a&~u,f!==0?n=Dl(f):i!==0?n=Dl(i):l||(l=a&~t,l!==0&&(n=Dl(l)))),n===0?0:e!==0&&e!==n&&(e&u)===0&&(u=n&-n,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:n}function Va(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function dv(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function If(){var t=Kn;return Kn<<=1,(Kn&4194048)===0&&(Kn=256),t}function Pf(){var t=kn;return kn<<=1,(kn&62914560)===0&&(kn=4194304),t}function gi(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function Qa(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function mv(t,e,l,a,n,u){var i=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var f=t.entanglements,r=t.expirationTimes,p=t.hiddenUpdates;for(l=i&~l;0<l;){var N=31-ce(l),D=1<<N;f[N]=0,r[N]=-1;var x=p[N];if(x!==null)for(p[N]=null,N=0;N<x.length;N++){var A=x[N];A!==null&&(A.lane&=-536870913)}l&=~D}a!==0&&ts(t,a,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(i&~e))}function ts(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-ce(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function es(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-ce(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function bi(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function pi(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function ls(){var t=B.p;return t!==0?t:(t=window.event,t===void 0?32:Hd(t.type))}function vv(t,e){var l=B.p;try{return B.p=t,e()}finally{B.p=l}}var il=Math.random().toString(36).slice(2),$t="__reactFiber$"+il,Pt="__reactProps$"+il,Pl="__reactContainer$"+il,Si="__reactEvents$"+il,hv="__reactListeners$"+il,yv="__reactHandles$"+il,as="__reactResources$"+il,La="__reactMarker$"+il;function xi(t){delete t[$t],delete t[Pt],delete t[Si],delete t[hv],delete t[yv]}function ta(t){var e=t[$t];if(e)return e;for(var l=t.parentNode;l;){if(e=l[Pl]||l[$t]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=xd(t);t!==null;){if(l=t[$t])return l;t=xd(t)}return e}t=l,l=t.parentNode}return null}function ea(t){if(t=t[$t]||t[Pl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Za(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(s(33))}function la(t){var e=t[as];return e||(e=t[as]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Xt(t){t[La]=!0}var ns=new Set,us={};function Ul(t,e){aa(t,e),aa(t+"Capture",e)}function aa(t,e){for(us[t]=e,t=0;t<e.length;t++)ns.add(e[t])}var gv=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),is={},cs={};function bv(t){return nl.call(cs,t)?!0:nl.call(is,t)?!1:gv.test(t)?cs[t]=!0:(is[t]=!0,!1)}function $n(t,e,l){if(bv(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Wn(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function He(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var Ti,fs;function na(t){if(Ti===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);Ti=e&&e[1]||"",fs=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Ti+t+fs}var Ai=!1;function Ei(t,e){if(!t||Ai)return"";Ai=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var D=function(){throw Error()};if(Object.defineProperty(D.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(D,[])}catch(A){var x=A}Reflect.construct(t,[],D)}else{try{D.call()}catch(A){x=A}t.call(D.prototype)}}else{try{throw Error()}catch(A){x=A}(D=t())&&typeof D.catch=="function"&&D.catch(function(){})}}catch(A){if(A&&x&&typeof A.stack=="string")return[A.stack,x.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),i=u[0],f=u[1];if(i&&f){var r=i.split(`
`),p=f.split(`
`);for(n=a=0;a<r.length&&!r[a].includes("DetermineComponentFrameRoot");)a++;for(;n<p.length&&!p[n].includes("DetermineComponentFrameRoot");)n++;if(a===r.length||n===p.length)for(a=r.length-1,n=p.length-1;1<=a&&0<=n&&r[a]!==p[n];)n--;for(;1<=a&&0<=n;a--,n--)if(r[a]!==p[n]){if(a!==1||n!==1)do if(a--,n--,0>n||r[a]!==p[n]){var N=`
`+r[a].replace(" at new "," at ");return t.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",t.displayName)),N}while(1<=a&&0<=n);break}}}finally{Ai=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?na(l):""}function pv(t){switch(t.tag){case 26:case 27:case 5:return na(t.type);case 16:return na("Lazy");case 13:return na("Suspense");case 19:return na("SuspenseList");case 0:case 15:return Ei(t.type,!1);case 11:return Ei(t.type.render,!1);case 1:return Ei(t.type,!0);case 31:return na("Activity");default:return""}}function ss(t){try{var e="";do e+=pv(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function be(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function os(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Sv(t){var e=os(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(i){a=""+i,u.call(this,i)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Fn(t){t._valueTracker||(t._valueTracker=Sv(t))}function rs(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=os(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function In(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var xv=/[\n"\\]/g;function pe(t){return t.replace(xv,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function _i(t,e,l,a,n,u,i,f){t.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?t.type=i:t.removeAttribute("type"),e!=null?i==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+be(e)):t.value!==""+be(e)&&(t.value=""+be(e)):i!=="submit"&&i!=="reset"||t.removeAttribute("value"),e!=null?Mi(t,i,be(e)):l!=null?Mi(t,i,be(l)):a!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.name=""+be(f):t.removeAttribute("name")}function ds(t,e,l,a,n,u,i,f){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;l=l!=null?""+be(l):"",e=e!=null?""+be(e):l,f||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=f?t.checked:!!a,t.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.name=i)}function Mi(t,e,l){e==="number"&&In(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function ua(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+be(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function ms(t,e,l){if(e!=null&&(e=""+be(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+be(l):""}function vs(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(s(92));if(zt(a)){if(1<a.length)throw Error(s(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=be(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function ia(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var Tv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function hs(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||Tv.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function ys(t,e,l){if(e!=null&&typeof e!="object")throw Error(s(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&hs(t,n,a)}else for(var u in e)e.hasOwnProperty(u)&&hs(t,u,e[u])}function zi(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Av=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ev=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Pn(t){return Ev.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ni=null;function Oi(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ca=null,fa=null;function gs(t){var e=ea(t);if(e&&(t=e.stateNode)){var l=t[Pt]||null;t:switch(t=e.stateNode,e.type){case"input":if(_i(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+pe(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[Pt]||null;if(!n)throw Error(s(90));_i(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&rs(a)}break t;case"textarea":ms(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&ua(t,!!l.multiple,e,!1)}}}var Ri=!1;function bs(t,e,l){if(Ri)return t(e,l);Ri=!0;try{var a=t(e);return a}finally{if(Ri=!1,(ca!==null||fa!==null)&&(Bu(),ca&&(e=ca,t=fa,fa=ca=null,gs(e),t)))for(e=0;e<t.length;e++)gs(t[e])}}function Ka(t,e){var l=t.stateNode;if(l===null)return null;var a=l[Pt]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(s(231,e,typeof l));return l}var qe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Di=!1;if(qe)try{var ka={};Object.defineProperty(ka,"passive",{get:function(){Di=!0}}),window.addEventListener("test",ka,ka),window.removeEventListener("test",ka,ka)}catch{Di=!1}var cl=null,Ui=null,tu=null;function ps(){if(tu)return tu;var t,e=Ui,l=e.length,a,n="value"in cl?cl.value:cl.textContent,u=n.length;for(t=0;t<l&&e[t]===n[t];t++);var i=l-t;for(a=1;a<=i&&e[l-a]===n[u-a];a++);return tu=n.slice(t,1<a?1-a:void 0)}function eu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function lu(){return!0}function Ss(){return!1}function te(t){function e(l,a,n,u,i){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=i,this.currentTarget=null;for(var f in t)t.hasOwnProperty(f)&&(l=t[f],this[f]=l?l(u):u[f]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?lu:Ss,this.isPropagationStopped=Ss,this}return z(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=lu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=lu)},persist:function(){},isPersistent:lu}),e}var jl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},au=te(jl),Ja=z({},jl,{view:0,detail:0}),_v=te(Ja),ji,Ci,$a,nu=z({},Ja,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Hi,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==$a&&($a&&t.type==="mousemove"?(ji=t.screenX-$a.screenX,Ci=t.screenY-$a.screenY):Ci=ji=0,$a=t),ji)},movementY:function(t){return"movementY"in t?t.movementY:Ci}}),xs=te(nu),Mv=z({},nu,{dataTransfer:0}),zv=te(Mv),Nv=z({},Ja,{relatedTarget:0}),wi=te(Nv),Ov=z({},jl,{animationName:0,elapsedTime:0,pseudoElement:0}),Rv=te(Ov),Dv=z({},jl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Uv=te(Dv),jv=z({},jl,{data:0}),Ts=te(jv),Cv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Hv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function qv(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Hv[t])?!!e[t]:!1}function Hi(){return qv}var Bv=z({},Ja,{key:function(t){if(t.key){var e=Cv[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=eu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?wv[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Hi,charCode:function(t){return t.type==="keypress"?eu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?eu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Gv=te(Bv),Yv=z({},nu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),As=te(Yv),Xv=z({},Ja,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Hi}),Vv=te(Xv),Qv=z({},jl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Lv=te(Qv),Zv=z({},nu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Kv=te(Zv),kv=z({},jl,{newState:0,oldState:0}),Jv=te(kv),$v=[9,13,27,32],qi=qe&&"CompositionEvent"in window,Wa=null;qe&&"documentMode"in document&&(Wa=document.documentMode);var Wv=qe&&"TextEvent"in window&&!Wa,Es=qe&&(!qi||Wa&&8<Wa&&11>=Wa),_s=" ",Ms=!1;function zs(t,e){switch(t){case"keyup":return $v.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ns(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var sa=!1;function Fv(t,e){switch(t){case"compositionend":return Ns(e);case"keypress":return e.which!==32?null:(Ms=!0,_s);case"textInput":return t=e.data,t===_s&&Ms?null:t;default:return null}}function Iv(t,e){if(sa)return t==="compositionend"||!qi&&zs(t,e)?(t=ps(),tu=Ui=cl=null,sa=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Es&&e.locale!=="ko"?null:e.data;default:return null}}var Pv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Os(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Pv[t.type]:e==="textarea"}function Rs(t,e,l,a){ca?fa?fa.push(a):fa=[a]:ca=a,e=Lu(e,"onChange"),0<e.length&&(l=new au("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var Fa=null,Ia=null;function th(t){rd(t,0)}function uu(t){var e=Za(t);if(rs(e))return t}function Ds(t,e){if(t==="change")return e}var Us=!1;if(qe){var Bi;if(qe){var Gi="oninput"in document;if(!Gi){var js=document.createElement("div");js.setAttribute("oninput","return;"),Gi=typeof js.oninput=="function"}Bi=Gi}else Bi=!1;Us=Bi&&(!document.documentMode||9<document.documentMode)}function Cs(){Fa&&(Fa.detachEvent("onpropertychange",ws),Ia=Fa=null)}function ws(t){if(t.propertyName==="value"&&uu(Ia)){var e=[];Rs(e,Ia,t,Oi(t)),bs(th,e)}}function eh(t,e,l){t==="focusin"?(Cs(),Fa=e,Ia=l,Fa.attachEvent("onpropertychange",ws)):t==="focusout"&&Cs()}function lh(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return uu(Ia)}function ah(t,e){if(t==="click")return uu(e)}function nh(t,e){if(t==="input"||t==="change")return uu(e)}function uh(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var fe=typeof Object.is=="function"?Object.is:uh;function Pa(t,e){if(fe(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!nl.call(e,n)||!fe(t[n],e[n]))return!1}return!0}function Hs(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function qs(t,e){var l=Hs(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Hs(l)}}function Bs(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Bs(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Gs(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=In(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=In(t.document)}return e}function Yi(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var ih=qe&&"documentMode"in document&&11>=document.documentMode,oa=null,Xi=null,tn=null,Vi=!1;function Ys(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Vi||oa==null||oa!==In(a)||(a=oa,"selectionStart"in a&&Yi(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),tn&&Pa(tn,a)||(tn=a,a=Lu(Xi,"onSelect"),0<a.length&&(e=new au("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=oa)))}function Cl(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var ra={animationend:Cl("Animation","AnimationEnd"),animationiteration:Cl("Animation","AnimationIteration"),animationstart:Cl("Animation","AnimationStart"),transitionrun:Cl("Transition","TransitionRun"),transitionstart:Cl("Transition","TransitionStart"),transitioncancel:Cl("Transition","TransitionCancel"),transitionend:Cl("Transition","TransitionEnd")},Qi={},Xs={};qe&&(Xs=document.createElement("div").style,"AnimationEvent"in window||(delete ra.animationend.animation,delete ra.animationiteration.animation,delete ra.animationstart.animation),"TransitionEvent"in window||delete ra.transitionend.transition);function wl(t){if(Qi[t])return Qi[t];if(!ra[t])return t;var e=ra[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in Xs)return Qi[t]=e[l];return t}var Vs=wl("animationend"),Qs=wl("animationiteration"),Ls=wl("animationstart"),ch=wl("transitionrun"),fh=wl("transitionstart"),sh=wl("transitioncancel"),Zs=wl("transitionend"),Ks=new Map,Li="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Li.push("scrollEnd");function ze(t,e){Ks.set(t,e),Ul(e,[t])}var ks=new WeakMap;function Se(t,e){if(typeof t=="object"&&t!==null){var l=ks.get(t);return l!==void 0?l:(e={value:t,source:e,stack:ss(e)},ks.set(t,e),e)}return{value:t,source:e,stack:ss(e)}}var xe=[],da=0,Zi=0;function iu(){for(var t=da,e=Zi=da=0;e<t;){var l=xe[e];xe[e++]=null;var a=xe[e];xe[e++]=null;var n=xe[e];xe[e++]=null;var u=xe[e];if(xe[e++]=null,a!==null&&n!==null){var i=a.pending;i===null?n.next=n:(n.next=i.next,i.next=n),a.pending=n}u!==0&&Js(l,n,u)}}function cu(t,e,l,a){xe[da++]=t,xe[da++]=e,xe[da++]=l,xe[da++]=a,Zi|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Ki(t,e,l,a){return cu(t,e,l,a),fu(t)}function ma(t,e){return cu(t,null,null,e),fu(t)}function Js(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=t.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,n&&e!==null&&(n=31-ce(l),t=u.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),u):null}function fu(t){if(50<Mn)throw Mn=0,Ic=null,Error(s(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var va={};function oh(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function se(t,e,l,a){return new oh(t,e,l,a)}function ki(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Be(t,e){var l=t.alternate;return l===null?(l=se(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function $s(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function su(t,e,l,a,n,u){var i=0;if(a=t,typeof t=="function")ki(t)&&(i=1);else if(typeof t=="string")i=dy(t,l,V.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Nt:return t=se(31,l,e,n),t.elementType=Nt,t.lanes=u,t;case F:return Hl(l.children,n,u,e);case $:i=8,n|=24;break;case K:return t=se(12,l,e,n|2),t.elementType=K,t.lanes=u,t;case j:return t=se(13,l,e,n),t.elementType=j,t.lanes=u,t;case lt:return t=se(19,l,e,n),t.elementType=lt,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case ct:case et:i=10;break t;case gt:i=9;break t;case ft:i=11;break t;case bt:i=14;break t;case G:i=16,a=null;break t}i=29,l=Error(s(130,t===null?"null":typeof t,"")),a=null}return e=se(i,l,e,n),e.elementType=t,e.type=a,e.lanes=u,e}function Hl(t,e,l,a){return t=se(7,t,a,e),t.lanes=l,t}function Ji(t,e,l){return t=se(6,t,null,e),t.lanes=l,t}function $i(t,e,l){return e=se(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ha=[],ya=0,ou=null,ru=0,Te=[],Ae=0,ql=null,Ge=1,Ye="";function Bl(t,e){ha[ya++]=ru,ha[ya++]=ou,ou=t,ru=e}function Ws(t,e,l){Te[Ae++]=Ge,Te[Ae++]=Ye,Te[Ae++]=ql,ql=t;var a=Ge;t=Ye;var n=32-ce(a)-1;a&=~(1<<n),l+=1;var u=32-ce(e)+n;if(30<u){var i=n-n%5;u=(a&(1<<i)-1).toString(32),a>>=i,n-=i,Ge=1<<32-ce(e)+n|l<<n|a,Ye=u+t}else Ge=1<<u|l<<n|a,Ye=t}function Wi(t){t.return!==null&&(Bl(t,1),Ws(t,1,0))}function Fi(t){for(;t===ou;)ou=ha[--ya],ha[ya]=null,ru=ha[--ya],ha[ya]=null;for(;t===ql;)ql=Te[--Ae],Te[Ae]=null,Ye=Te[--Ae],Te[Ae]=null,Ge=Te[--Ae],Te[Ae]=null}var It=null,Ut=null,ht=!1,Gl=null,De=!1,Ii=Error(s(519));function Yl(t){var e=Error(s(418,""));throw an(Se(e,t)),Ii}function Fs(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[$t]=t,e[Pt]=a,l){case"dialog":rt("cancel",e),rt("close",e);break;case"iframe":case"object":case"embed":rt("load",e);break;case"video":case"audio":for(l=0;l<Nn.length;l++)rt(Nn[l],e);break;case"source":rt("error",e);break;case"img":case"image":case"link":rt("error",e),rt("load",e);break;case"details":rt("toggle",e);break;case"input":rt("invalid",e),ds(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Fn(e);break;case"select":rt("invalid",e);break;case"textarea":rt("invalid",e),vs(e,a.value,a.defaultValue,a.children),Fn(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||hd(e.textContent,l)?(a.popover!=null&&(rt("beforetoggle",e),rt("toggle",e)),a.onScroll!=null&&rt("scroll",e),a.onScrollEnd!=null&&rt("scrollend",e),a.onClick!=null&&(e.onclick=Zu),e=!0):e=!1,e||Yl(t)}function Is(t){for(It=t.return;It;)switch(It.tag){case 5:case 13:De=!1;return;case 27:case 3:De=!0;return;default:It=It.return}}function en(t){if(t!==It)return!1;if(!ht)return Is(t),ht=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||hf(t.type,t.memoizedProps)),l=!l),l&&Ut&&Yl(t),Is(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Ut=Oe(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Ut=null}}else e===27?(e=Ut,Al(t.type)?(t=pf,pf=null,Ut=t):Ut=e):Ut=It?Oe(t.stateNode.nextSibling):null;return!0}function ln(){Ut=It=null,ht=!1}function Ps(){var t=Gl;return t!==null&&(ae===null?ae=t:ae.push.apply(ae,t),Gl=null),t}function an(t){Gl===null?Gl=[t]:Gl.push(t)}var Pi=U(null),Xl=null,Xe=null;function fl(t,e,l){w(Pi,e._currentValue),e._currentValue=l}function Ve(t){t._currentValue=Pi.current,X(Pi)}function tc(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function ec(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var i=n.child;u=u.firstContext;t:for(;u!==null;){var f=u;u=n;for(var r=0;r<e.length;r++)if(f.context===e[r]){u.lanes|=l,f=u.alternate,f!==null&&(f.lanes|=l),tc(u.return,l,t),a||(i=null);break t}u=f.next}}else if(n.tag===18){if(i=n.return,i===null)throw Error(s(341));i.lanes|=l,u=i.alternate,u!==null&&(u.lanes|=l),tc(i,l,t),i=null}else i=n.child;if(i!==null)i.return=n;else for(i=n;i!==null;){if(i===t){i=null;break}if(n=i.sibling,n!==null){n.return=i.return,i=n;break}i=i.return}n=i}}function nn(t,e,l,a){t=null;for(var n=e,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var i=n.alternate;if(i===null)throw Error(s(387));if(i=i.memoizedProps,i!==null){var f=n.type;fe(n.pendingProps.value,i.value)||(t!==null?t.push(f):t=[f])}}else if(n===mt.current){if(i=n.alternate,i===null)throw Error(s(387));i.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Cn):t=[Cn])}n=n.return}t!==null&&ec(e,t,l,a),e.flags|=262144}function du(t){for(t=t.firstContext;t!==null;){if(!fe(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Vl(t){Xl=t,Xe=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Wt(t){return to(Xl,t)}function mu(t,e){return Xl===null&&Vl(t),to(t,e)}function to(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},Xe===null){if(t===null)throw Error(s(308));Xe=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Xe=Xe.next=e;return l}var rh=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},dh=c.unstable_scheduleCallback,mh=c.unstable_NormalPriority,Bt={$$typeof:et,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function lc(){return{controller:new rh,data:new Map,refCount:0}}function un(t){t.refCount--,t.refCount===0&&dh(mh,function(){t.controller.abort()})}var cn=null,ac=0,ga=0,ba=null;function vh(t,e){if(cn===null){var l=cn=[];ac=0,ga=uf(),ba={status:"pending",value:void 0,then:function(a){l.push(a)}}}return ac++,e.then(eo,eo),e}function eo(){if(--ac===0&&cn!==null){ba!==null&&(ba.status="fulfilled");var t=cn;cn=null,ga=0,ba=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function hh(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var lo=_.S;_.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&vh(t,e),lo!==null&&lo(t,e)};var Ql=U(null);function nc(){var t=Ql.current;return t!==null?t:Mt.pooledCache}function vu(t,e){e===null?w(Ql,Ql.current):w(Ql,e.pool)}function ao(){var t=nc();return t===null?null:{parent:Bt._currentValue,pool:t}}var fn=Error(s(460)),no=Error(s(474)),hu=Error(s(542)),uc={then:function(){}};function uo(t){return t=t.status,t==="fulfilled"||t==="rejected"}function yu(){}function io(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(yu,yu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,fo(t),t;default:if(typeof e.status=="string")e.then(yu,yu);else{if(t=Mt,t!==null&&100<t.shellSuspendCounter)throw Error(s(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,fo(t),t}throw sn=e,fn}}var sn=null;function co(){if(sn===null)throw Error(s(459));var t=sn;return sn=null,t}function fo(t){if(t===fn||t===hu)throw Error(s(483))}var sl=!1;function ic(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function cc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function ol(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function rl(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(pt&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=fu(t),Js(t,null,l),e}return cu(t,a,e,l),fu(t)}function on(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,es(t,l)}}function fc(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var i={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=i:u=u.next=i,l=l.next}while(l!==null);u===null?n=u=e:u=u.next=e}else n=u=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var sc=!1;function rn(){if(sc){var t=ba;if(t!==null)throw t}}function dn(t,e,l,a){sc=!1;var n=t.updateQueue;sl=!1;var u=n.firstBaseUpdate,i=n.lastBaseUpdate,f=n.shared.pending;if(f!==null){n.shared.pending=null;var r=f,p=r.next;r.next=null,i===null?u=p:i.next=p,i=r;var N=t.alternate;N!==null&&(N=N.updateQueue,f=N.lastBaseUpdate,f!==i&&(f===null?N.firstBaseUpdate=p:f.next=p,N.lastBaseUpdate=r))}if(u!==null){var D=n.baseState;i=0,N=p=r=null,f=u;do{var x=f.lane&-536870913,A=x!==f.lane;if(A?(dt&x)===x:(a&x)===x){x!==0&&x===ga&&(sc=!0),N!==null&&(N=N.next={lane:0,tag:f.tag,payload:f.payload,callback:null,next:null});t:{var I=t,J=f;x=e;var At=l;switch(J.tag){case 1:if(I=J.payload,typeof I=="function"){D=I.call(At,D,x);break t}D=I;break t;case 3:I.flags=I.flags&-65537|128;case 0:if(I=J.payload,x=typeof I=="function"?I.call(At,D,x):I,x==null)break t;D=z({},D,x);break t;case 2:sl=!0}}x=f.callback,x!==null&&(t.flags|=64,A&&(t.flags|=8192),A=n.callbacks,A===null?n.callbacks=[x]:A.push(x))}else A={lane:x,tag:f.tag,payload:f.payload,callback:f.callback,next:null},N===null?(p=N=A,r=D):N=N.next=A,i|=x;if(f=f.next,f===null){if(f=n.shared.pending,f===null)break;A=f,f=A.next,A.next=null,n.lastBaseUpdate=A,n.shared.pending=null}}while(!0);N===null&&(r=D),n.baseState=r,n.firstBaseUpdate=p,n.lastBaseUpdate=N,u===null&&(n.shared.lanes=0),pl|=i,t.lanes=i,t.memoizedState=D}}function so(t,e){if(typeof t!="function")throw Error(s(191,t));t.call(e)}function oo(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)so(l[t],e)}var pa=U(null),gu=U(0);function ro(t,e){t=$e,w(gu,t),w(pa,e),$e=t|e.baseLanes}function oc(){w(gu,$e),w(pa,pa.current)}function rc(){$e=gu.current,X(pa),X(gu)}var dl=0,nt=null,xt=null,Ht=null,bu=!1,Sa=!1,Ll=!1,pu=0,mn=0,xa=null,yh=0;function Ct(){throw Error(s(321))}function dc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!fe(t[l],e[l]))return!1;return!0}function mc(t,e,l,a,n,u){return dl=u,nt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,_.H=t===null||t.memoizedState===null?$o:Wo,Ll=!1,u=l(a,n),Ll=!1,Sa&&(u=vo(e,l,a,n)),mo(t),u}function mo(t){_.H=_u;var e=xt!==null&&xt.next!==null;if(dl=0,Ht=xt=nt=null,bu=!1,mn=0,xa=null,e)throw Error(s(300));t===null||Vt||(t=t.dependencies,t!==null&&du(t)&&(Vt=!0))}function vo(t,e,l,a){nt=t;var n=0;do{if(Sa&&(xa=null),mn=0,Sa=!1,25<=n)throw Error(s(301));if(n+=1,Ht=xt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}_.H=Ah,u=e(l,a)}while(Sa);return u}function gh(){var t=_.H,e=t.useState()[0];return e=typeof e.then=="function"?vn(e):e,t=t.useState()[0],(xt!==null?xt.memoizedState:null)!==t&&(nt.flags|=1024),e}function vc(){var t=pu!==0;return pu=0,t}function hc(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function yc(t){if(bu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}bu=!1}dl=0,Ht=xt=nt=null,Sa=!1,mn=pu=0,xa=null}function ee(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ht===null?nt.memoizedState=Ht=t:Ht=Ht.next=t,Ht}function qt(){if(xt===null){var t=nt.alternate;t=t!==null?t.memoizedState:null}else t=xt.next;var e=Ht===null?nt.memoizedState:Ht.next;if(e!==null)Ht=e,xt=t;else{if(t===null)throw nt.alternate===null?Error(s(467)):Error(s(310));xt=t,t={memoizedState:xt.memoizedState,baseState:xt.baseState,baseQueue:xt.baseQueue,queue:xt.queue,next:null},Ht===null?nt.memoizedState=Ht=t:Ht=Ht.next=t}return Ht}function gc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function vn(t){var e=mn;return mn+=1,xa===null&&(xa=[]),t=io(xa,t,e),e=nt,(Ht===null?e.memoizedState:Ht.next)===null&&(e=e.alternate,_.H=e===null||e.memoizedState===null?$o:Wo),t}function Su(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return vn(t);if(t.$$typeof===et)return Wt(t)}throw Error(s(438,String(t)))}function bc(t){var e=null,l=nt.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=nt.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=gc(),nt.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=he;return e.index++,l}function Qe(t,e){return typeof e=="function"?e(t):e}function xu(t){var e=qt();return pc(e,xt,t)}function pc(t,e,l){var a=t.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=l;var n=t.baseQueue,u=a.pending;if(u!==null){if(n!==null){var i=n.next;n.next=u.next,u.next=i}e.baseQueue=n=u,a.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{e=n.next;var f=i=null,r=null,p=e,N=!1;do{var D=p.lane&-536870913;if(D!==p.lane?(dt&D)===D:(dl&D)===D){var x=p.revertLane;if(x===0)r!==null&&(r=r.next={lane:0,revertLane:0,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null}),D===ga&&(N=!0);else if((dl&x)===x){p=p.next,x===ga&&(N=!0);continue}else D={lane:0,revertLane:p.revertLane,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null},r===null?(f=r=D,i=u):r=r.next=D,nt.lanes|=x,pl|=x;D=p.action,Ll&&l(u,D),u=p.hasEagerState?p.eagerState:l(u,D)}else x={lane:D,revertLane:p.revertLane,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null},r===null?(f=r=x,i=u):r=r.next=x,nt.lanes|=D,pl|=D;p=p.next}while(p!==null&&p!==e);if(r===null?i=u:r.next=f,!fe(u,t.memoizedState)&&(Vt=!0,N&&(l=ba,l!==null)))throw l;t.memoizedState=u,t.baseState=i,t.baseQueue=r,a.lastRenderedState=u}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function Sc(t){var e=qt(),l=e.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,u=e.memoizedState;if(n!==null){l.pending=null;var i=n=n.next;do u=t(u,i.action),i=i.next;while(i!==n);fe(u,e.memoizedState)||(Vt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,a]}function ho(t,e,l){var a=nt,n=qt(),u=ht;if(u){if(l===void 0)throw Error(s(407));l=l()}else l=e();var i=!fe((xt||n).memoizedState,l);i&&(n.memoizedState=l,Vt=!0),n=n.queue;var f=bo.bind(null,a,n,t);if(hn(2048,8,f,[t]),n.getSnapshot!==e||i||Ht!==null&&Ht.memoizedState.tag&1){if(a.flags|=2048,Ta(9,Tu(),go.bind(null,a,n,l,e),null),Mt===null)throw Error(s(349));u||(dl&124)!==0||yo(a,e,l)}return l}function yo(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=nt.updateQueue,e===null?(e=gc(),nt.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function go(t,e,l,a){e.value=l,e.getSnapshot=a,po(e)&&So(t)}function bo(t,e,l){return l(function(){po(e)&&So(t)})}function po(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!fe(t,l)}catch{return!0}}function So(t){var e=ma(t,2);e!==null&&ve(e,t,2)}function xc(t){var e=ee();if(typeof t=="function"){var l=t;if(t=l(),Ll){ul(!0);try{l()}finally{ul(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qe,lastRenderedState:t},e}function xo(t,e,l,a){return t.baseState=l,pc(t,xt,typeof a=="function"?a:Qe)}function bh(t,e,l,a,n){if(Eu(t))throw Error(s(485));if(t=e.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){u.listeners.push(i)}};_.T!==null?l(!0):u.isTransition=!1,a(u),l=e.pending,l===null?(u.next=e.pending=u,To(e,u)):(u.next=l.next,e.pending=l.next=u)}}function To(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var u=_.T,i={};_.T=i;try{var f=l(n,a),r=_.S;r!==null&&r(i,f),Ao(t,e,f)}catch(p){Tc(t,e,p)}finally{_.T=u}}else try{u=l(n,a),Ao(t,e,u)}catch(p){Tc(t,e,p)}}function Ao(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Eo(t,e,a)},function(a){return Tc(t,e,a)}):Eo(t,e,l)}function Eo(t,e,l){e.status="fulfilled",e.value=l,_o(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,To(t,l)))}function Tc(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,_o(e),e=e.next;while(e!==a)}t.action=null}function _o(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Mo(t,e){return e}function zo(t,e){if(ht){var l=Mt.formState;if(l!==null){t:{var a=nt;if(ht){if(Ut){e:{for(var n=Ut,u=De;n.nodeType!==8;){if(!u){n=null;break e}if(n=Oe(n.nextSibling),n===null){n=null;break e}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Ut=Oe(n.nextSibling),a=n.data==="F!";break t}}Yl(a)}a=!1}a&&(e=l[0])}}return l=ee(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Mo,lastRenderedState:e},l.queue=a,l=Ko.bind(null,nt,a),a.dispatch=l,a=xc(!1),u=zc.bind(null,nt,!1,a.queue),a=ee(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=bh.bind(null,nt,n,u,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function No(t){var e=qt();return Oo(e,xt,t)}function Oo(t,e,l){if(e=pc(t,e,Mo)[0],t=xu(Qe)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=vn(e)}catch(i){throw i===fn?hu:i}else a=e;e=qt();var n=e.queue,u=n.dispatch;return l!==e.memoizedState&&(nt.flags|=2048,Ta(9,Tu(),ph.bind(null,n,l),null)),[a,u,t]}function ph(t,e){t.action=e}function Ro(t){var e=qt(),l=xt;if(l!==null)return Oo(e,l,t);qt(),e=e.memoizedState,l=qt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function Ta(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=nt.updateQueue,e===null&&(e=gc(),nt.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function Tu(){return{destroy:void 0,resource:void 0}}function Do(){return qt().memoizedState}function Au(t,e,l,a){var n=ee();a=a===void 0?null:a,nt.flags|=t,n.memoizedState=Ta(1|e,Tu(),l,a)}function hn(t,e,l,a){var n=qt();a=a===void 0?null:a;var u=n.memoizedState.inst;xt!==null&&a!==null&&dc(a,xt.memoizedState.deps)?n.memoizedState=Ta(e,u,l,a):(nt.flags|=t,n.memoizedState=Ta(1|e,u,l,a))}function Uo(t,e){Au(8390656,8,t,e)}function jo(t,e){hn(2048,8,t,e)}function Co(t,e){return hn(4,2,t,e)}function wo(t,e){return hn(4,4,t,e)}function Ho(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function qo(t,e,l){l=l!=null?l.concat([t]):null,hn(4,4,Ho.bind(null,e,t),l)}function Ac(){}function Bo(t,e){var l=qt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&dc(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function Go(t,e){var l=qt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&dc(e,a[1]))return a[0];if(a=t(),Ll){ul(!0);try{t()}finally{ul(!1)}}return l.memoizedState=[a,e],a}function Ec(t,e,l){return l===void 0||(dl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Vr(),nt.lanes|=t,pl|=t,l)}function Yo(t,e,l,a){return fe(l,e)?l:pa.current!==null?(t=Ec(t,l,a),fe(t,e)||(Vt=!0),t):(dl&42)===0?(Vt=!0,t.memoizedState=l):(t=Vr(),nt.lanes|=t,pl|=t,e)}function Xo(t,e,l,a,n){var u=B.p;B.p=u!==0&&8>u?u:8;var i=_.T,f={};_.T=f,zc(t,!1,e,l);try{var r=n(),p=_.S;if(p!==null&&p(f,r),r!==null&&typeof r=="object"&&typeof r.then=="function"){var N=hh(r,a);yn(t,e,N,me(t))}else yn(t,e,a,me(t))}catch(D){yn(t,e,{then:function(){},status:"rejected",reason:D},me())}finally{B.p=u,_.T=i}}function Sh(){}function _c(t,e,l,a){if(t.tag!==5)throw Error(s(476));var n=Vo(t).queue;Xo(t,n,e,C,l===null?Sh:function(){return Qo(t),l(a)})}function Vo(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:C,baseState:C,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qe,lastRenderedState:C},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qe,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Qo(t){var e=Vo(t).next.queue;yn(t,e,{},me())}function Mc(){return Wt(Cn)}function Lo(){return qt().memoizedState}function Zo(){return qt().memoizedState}function xh(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=me();t=ol(l);var a=rl(e,t,l);a!==null&&(ve(a,e,l),on(a,e,l)),e={cache:lc()},t.payload=e;return}e=e.return}}function Th(t,e,l){var a=me();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Eu(t)?ko(e,l):(l=Ki(t,e,l,a),l!==null&&(ve(l,t,a),Jo(l,e,a)))}function Ko(t,e,l){var a=me();yn(t,e,l,a)}function yn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Eu(t))ko(e,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var i=e.lastRenderedState,f=u(i,l);if(n.hasEagerState=!0,n.eagerState=f,fe(f,i))return cu(t,e,n,0),Mt===null&&iu(),!1}catch{}finally{}if(l=Ki(t,e,n,a),l!==null)return ve(l,t,a),Jo(l,e,a),!0}return!1}function zc(t,e,l,a){if(a={lane:2,revertLane:uf(),action:a,hasEagerState:!1,eagerState:null,next:null},Eu(t)){if(e)throw Error(s(479))}else e=Ki(t,l,a,2),e!==null&&ve(e,t,2)}function Eu(t){var e=t.alternate;return t===nt||e!==null&&e===nt}function ko(t,e){Sa=bu=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Jo(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,es(t,l)}}var _u={readContext:Wt,use:Su,useCallback:Ct,useContext:Ct,useEffect:Ct,useImperativeHandle:Ct,useLayoutEffect:Ct,useInsertionEffect:Ct,useMemo:Ct,useReducer:Ct,useRef:Ct,useState:Ct,useDebugValue:Ct,useDeferredValue:Ct,useTransition:Ct,useSyncExternalStore:Ct,useId:Ct,useHostTransitionStatus:Ct,useFormState:Ct,useActionState:Ct,useOptimistic:Ct,useMemoCache:Ct,useCacheRefresh:Ct},$o={readContext:Wt,use:Su,useCallback:function(t,e){return ee().memoizedState=[t,e===void 0?null:e],t},useContext:Wt,useEffect:Uo,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Au(4194308,4,Ho.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Au(4194308,4,t,e)},useInsertionEffect:function(t,e){Au(4,2,t,e)},useMemo:function(t,e){var l=ee();e=e===void 0?null:e;var a=t();if(Ll){ul(!0);try{t()}finally{ul(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=ee();if(l!==void 0){var n=l(e);if(Ll){ul(!0);try{l(e)}finally{ul(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=Th.bind(null,nt,t),[a.memoizedState,t]},useRef:function(t){var e=ee();return t={current:t},e.memoizedState=t},useState:function(t){t=xc(t);var e=t.queue,l=Ko.bind(null,nt,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:Ac,useDeferredValue:function(t,e){var l=ee();return Ec(l,t,e)},useTransition:function(){var t=xc(!1);return t=Xo.bind(null,nt,t.queue,!0,!1),ee().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=nt,n=ee();if(ht){if(l===void 0)throw Error(s(407));l=l()}else{if(l=e(),Mt===null)throw Error(s(349));(dt&124)!==0||yo(a,e,l)}n.memoizedState=l;var u={value:l,getSnapshot:e};return n.queue=u,Uo(bo.bind(null,a,u,t),[t]),a.flags|=2048,Ta(9,Tu(),go.bind(null,a,u,l,e),null),l},useId:function(){var t=ee(),e=Mt.identifierPrefix;if(ht){var l=Ye,a=Ge;l=(a&~(1<<32-ce(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=pu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=yh++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Mc,useFormState:zo,useActionState:zo,useOptimistic:function(t){var e=ee();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=zc.bind(null,nt,!0,l),l.dispatch=e,[t,e]},useMemoCache:bc,useCacheRefresh:function(){return ee().memoizedState=xh.bind(null,nt)}},Wo={readContext:Wt,use:Su,useCallback:Bo,useContext:Wt,useEffect:jo,useImperativeHandle:qo,useInsertionEffect:Co,useLayoutEffect:wo,useMemo:Go,useReducer:xu,useRef:Do,useState:function(){return xu(Qe)},useDebugValue:Ac,useDeferredValue:function(t,e){var l=qt();return Yo(l,xt.memoizedState,t,e)},useTransition:function(){var t=xu(Qe)[0],e=qt().memoizedState;return[typeof t=="boolean"?t:vn(t),e]},useSyncExternalStore:ho,useId:Lo,useHostTransitionStatus:Mc,useFormState:No,useActionState:No,useOptimistic:function(t,e){var l=qt();return xo(l,xt,t,e)},useMemoCache:bc,useCacheRefresh:Zo},Ah={readContext:Wt,use:Su,useCallback:Bo,useContext:Wt,useEffect:jo,useImperativeHandle:qo,useInsertionEffect:Co,useLayoutEffect:wo,useMemo:Go,useReducer:Sc,useRef:Do,useState:function(){return Sc(Qe)},useDebugValue:Ac,useDeferredValue:function(t,e){var l=qt();return xt===null?Ec(l,t,e):Yo(l,xt.memoizedState,t,e)},useTransition:function(){var t=Sc(Qe)[0],e=qt().memoizedState;return[typeof t=="boolean"?t:vn(t),e]},useSyncExternalStore:ho,useId:Lo,useHostTransitionStatus:Mc,useFormState:Ro,useActionState:Ro,useOptimistic:function(t,e){var l=qt();return xt!==null?xo(l,xt,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:bc,useCacheRefresh:Zo},Aa=null,gn=0;function Mu(t){var e=gn;return gn+=1,Aa===null&&(Aa=[]),io(Aa,t,e)}function bn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function zu(t,e){throw e.$$typeof===H?Error(s(525)):(t=Object.prototype.toString.call(e),Error(s(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Fo(t){var e=t._init;return e(t._payload)}function Io(t){function e(y,v){if(t){var b=y.deletions;b===null?(y.deletions=[v],y.flags|=16):b.push(v)}}function l(y,v){if(!t)return null;for(;v!==null;)e(y,v),v=v.sibling;return null}function a(y){for(var v=new Map;y!==null;)y.key!==null?v.set(y.key,y):v.set(y.index,y),y=y.sibling;return v}function n(y,v){return y=Be(y,v),y.index=0,y.sibling=null,y}function u(y,v,b){return y.index=b,t?(b=y.alternate,b!==null?(b=b.index,b<v?(y.flags|=67108866,v):b):(y.flags|=67108866,v)):(y.flags|=1048576,v)}function i(y){return t&&y.alternate===null&&(y.flags|=67108866),y}function f(y,v,b,O){return v===null||v.tag!==6?(v=Ji(b,y.mode,O),v.return=y,v):(v=n(v,b),v.return=y,v)}function r(y,v,b,O){var Q=b.type;return Q===F?N(y,v,b.props.children,O,b.key):v!==null&&(v.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===G&&Fo(Q)===v.type)?(v=n(v,b.props),bn(v,b),v.return=y,v):(v=su(b.type,b.key,b.props,null,y.mode,O),bn(v,b),v.return=y,v)}function p(y,v,b,O){return v===null||v.tag!==4||v.stateNode.containerInfo!==b.containerInfo||v.stateNode.implementation!==b.implementation?(v=$i(b,y.mode,O),v.return=y,v):(v=n(v,b.children||[]),v.return=y,v)}function N(y,v,b,O,Q){return v===null||v.tag!==7?(v=Hl(b,y.mode,O,Q),v.return=y,v):(v=n(v,b),v.return=y,v)}function D(y,v,b){if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return v=Ji(""+v,y.mode,b),v.return=y,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Y:return b=su(v.type,v.key,v.props,null,y.mode,b),bn(b,v),b.return=y,b;case tt:return v=$i(v,y.mode,b),v.return=y,v;case G:var O=v._init;return v=O(v._payload),D(y,v,b)}if(zt(v)||Rt(v))return v=Hl(v,y.mode,b,null),v.return=y,v;if(typeof v.then=="function")return D(y,Mu(v),b);if(v.$$typeof===et)return D(y,mu(y,v),b);zu(y,v)}return null}function x(y,v,b,O){var Q=v!==null?v.key:null;if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return Q!==null?null:f(y,v,""+b,O);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case Y:return b.key===Q?r(y,v,b,O):null;case tt:return b.key===Q?p(y,v,b,O):null;case G:return Q=b._init,b=Q(b._payload),x(y,v,b,O)}if(zt(b)||Rt(b))return Q!==null?null:N(y,v,b,O,null);if(typeof b.then=="function")return x(y,v,Mu(b),O);if(b.$$typeof===et)return x(y,v,mu(y,b),O);zu(y,b)}return null}function A(y,v,b,O,Q){if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return y=y.get(b)||null,f(v,y,""+O,Q);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case Y:return y=y.get(O.key===null?b:O.key)||null,r(v,y,O,Q);case tt:return y=y.get(O.key===null?b:O.key)||null,p(v,y,O,Q);case G:var it=O._init;return O=it(O._payload),A(y,v,b,O,Q)}if(zt(O)||Rt(O))return y=y.get(b)||null,N(v,y,O,Q,null);if(typeof O.then=="function")return A(y,v,b,Mu(O),Q);if(O.$$typeof===et)return A(y,v,b,mu(v,O),Q);zu(v,O)}return null}function I(y,v,b,O){for(var Q=null,it=null,k=v,W=v=0,Lt=null;k!==null&&W<b.length;W++){k.index>W?(Lt=k,k=null):Lt=k.sibling;var vt=x(y,k,b[W],O);if(vt===null){k===null&&(k=Lt);break}t&&k&&vt.alternate===null&&e(y,k),v=u(vt,v,W),it===null?Q=vt:it.sibling=vt,it=vt,k=Lt}if(W===b.length)return l(y,k),ht&&Bl(y,W),Q;if(k===null){for(;W<b.length;W++)k=D(y,b[W],O),k!==null&&(v=u(k,v,W),it===null?Q=k:it.sibling=k,it=k);return ht&&Bl(y,W),Q}for(k=a(k);W<b.length;W++)Lt=A(k,y,W,b[W],O),Lt!==null&&(t&&Lt.alternate!==null&&k.delete(Lt.key===null?W:Lt.key),v=u(Lt,v,W),it===null?Q=Lt:it.sibling=Lt,it=Lt);return t&&k.forEach(function(Nl){return e(y,Nl)}),ht&&Bl(y,W),Q}function J(y,v,b,O){if(b==null)throw Error(s(151));for(var Q=null,it=null,k=v,W=v=0,Lt=null,vt=b.next();k!==null&&!vt.done;W++,vt=b.next()){k.index>W?(Lt=k,k=null):Lt=k.sibling;var Nl=x(y,k,vt.value,O);if(Nl===null){k===null&&(k=Lt);break}t&&k&&Nl.alternate===null&&e(y,k),v=u(Nl,v,W),it===null?Q=Nl:it.sibling=Nl,it=Nl,k=Lt}if(vt.done)return l(y,k),ht&&Bl(y,W),Q;if(k===null){for(;!vt.done;W++,vt=b.next())vt=D(y,vt.value,O),vt!==null&&(v=u(vt,v,W),it===null?Q=vt:it.sibling=vt,it=vt);return ht&&Bl(y,W),Q}for(k=a(k);!vt.done;W++,vt=b.next())vt=A(k,y,W,vt.value,O),vt!==null&&(t&&vt.alternate!==null&&k.delete(vt.key===null?W:vt.key),v=u(vt,v,W),it===null?Q=vt:it.sibling=vt,it=vt);return t&&k.forEach(function(Ey){return e(y,Ey)}),ht&&Bl(y,W),Q}function At(y,v,b,O){if(typeof b=="object"&&b!==null&&b.type===F&&b.key===null&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case Y:t:{for(var Q=b.key;v!==null;){if(v.key===Q){if(Q=b.type,Q===F){if(v.tag===7){l(y,v.sibling),O=n(v,b.props.children),O.return=y,y=O;break t}}else if(v.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===G&&Fo(Q)===v.type){l(y,v.sibling),O=n(v,b.props),bn(O,b),O.return=y,y=O;break t}l(y,v);break}else e(y,v);v=v.sibling}b.type===F?(O=Hl(b.props.children,y.mode,O,b.key),O.return=y,y=O):(O=su(b.type,b.key,b.props,null,y.mode,O),bn(O,b),O.return=y,y=O)}return i(y);case tt:t:{for(Q=b.key;v!==null;){if(v.key===Q)if(v.tag===4&&v.stateNode.containerInfo===b.containerInfo&&v.stateNode.implementation===b.implementation){l(y,v.sibling),O=n(v,b.children||[]),O.return=y,y=O;break t}else{l(y,v);break}else e(y,v);v=v.sibling}O=$i(b,y.mode,O),O.return=y,y=O}return i(y);case G:return Q=b._init,b=Q(b._payload),At(y,v,b,O)}if(zt(b))return I(y,v,b,O);if(Rt(b)){if(Q=Rt(b),typeof Q!="function")throw Error(s(150));return b=Q.call(b),J(y,v,b,O)}if(typeof b.then=="function")return At(y,v,Mu(b),O);if(b.$$typeof===et)return At(y,v,mu(y,b),O);zu(y,b)}return typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint"?(b=""+b,v!==null&&v.tag===6?(l(y,v.sibling),O=n(v,b),O.return=y,y=O):(l(y,v),O=Ji(b,y.mode,O),O.return=y,y=O),i(y)):l(y,v)}return function(y,v,b,O){try{gn=0;var Q=At(y,v,b,O);return Aa=null,Q}catch(k){if(k===fn||k===hu)throw k;var it=se(29,k,null,y.mode);return it.lanes=O,it.return=y,it}finally{}}}var Ea=Io(!0),Po=Io(!1),Ee=U(null),Ue=null;function ml(t){var e=t.alternate;w(Gt,Gt.current&1),w(Ee,t),Ue===null&&(e===null||pa.current!==null||e.memoizedState!==null)&&(Ue=t)}function tr(t){if(t.tag===22){if(w(Gt,Gt.current),w(Ee,t),Ue===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ue=t)}}else vl()}function vl(){w(Gt,Gt.current),w(Ee,Ee.current)}function Le(t){X(Ee),Ue===t&&(Ue=null),X(Gt)}var Gt=U(0);function Nu(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||bf(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Nc(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:z({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var Oc={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=me(),n=ol(a);n.payload=e,l!=null&&(n.callback=l),e=rl(t,n,a),e!==null&&(ve(e,t,a),on(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=me(),n=ol(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=rl(t,n,a),e!==null&&(ve(e,t,a),on(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=me(),a=ol(l);a.tag=2,e!=null&&(a.callback=e),e=rl(t,a,l),e!==null&&(ve(e,t,l),on(e,t,l))}};function er(t,e,l,a,n,u,i){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,u,i):e.prototype&&e.prototype.isPureReactComponent?!Pa(l,a)||!Pa(n,u):!0}function lr(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&Oc.enqueueReplaceState(e,e.state,null)}function Zl(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=z({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var Ou=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function ar(t){Ou(t)}function nr(t){console.error(t)}function ur(t){Ou(t)}function Ru(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function ir(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Rc(t,e,l){return l=ol(l),l.tag=3,l.payload={element:null},l.callback=function(){Ru(t,e)},l}function cr(t){return t=ol(t),t.tag=3,t}function fr(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;t.payload=function(){return n(u)},t.callback=function(){ir(e,l,a)}}var i=l.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(t.callback=function(){ir(e,l,a),typeof n!="function"&&(Sl===null?Sl=new Set([this]):Sl.add(this));var f=a.stack;this.componentDidCatch(a.value,{componentStack:f!==null?f:""})})}function Eh(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&nn(e,l,n,!0),l=Ee.current,l!==null){switch(l.tag){case 13:return Ue===null?tf():l.alternate===null&&jt===0&&(jt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===uc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),lf(t,a,n)),!1;case 22:return l.flags|=65536,a===uc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),lf(t,a,n)),!1}throw Error(s(435,l.tag))}return lf(t,a,n),tf(),!1}if(ht)return e=Ee.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==Ii&&(t=Error(s(422),{cause:a}),an(Se(t,l)))):(a!==Ii&&(e=Error(s(423),{cause:a}),an(Se(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=Se(a,l),n=Rc(t.stateNode,a,n),fc(t,n),jt!==4&&(jt=2)),!1;var u=Error(s(520),{cause:a});if(u=Se(u,l),_n===null?_n=[u]:_n.push(u),jt!==4&&(jt=2),e===null)return!0;a=Se(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=Rc(l.stateNode,a,t),fc(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Sl===null||!Sl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=cr(n),fr(n,t,l,a),fc(l,n),!1}l=l.return}while(l!==null);return!1}var sr=Error(s(461)),Vt=!1;function Zt(t,e,l,a){e.child=t===null?Po(e,null,l,a):Ea(e,t.child,l,a)}function or(t,e,l,a,n){l=l.render;var u=e.ref;if("ref"in a){var i={};for(var f in a)f!=="ref"&&(i[f]=a[f])}else i=a;return Vl(e),a=mc(t,e,l,i,u,n),f=vc(),t!==null&&!Vt?(hc(t,e,n),Ze(t,e,n)):(ht&&f&&Wi(e),e.flags|=1,Zt(t,e,a,n),e.child)}function rr(t,e,l,a,n){if(t===null){var u=l.type;return typeof u=="function"&&!ki(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,dr(t,e,u,a,n)):(t=su(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Bc(t,n)){var i=u.memoizedProps;if(l=l.compare,l=l!==null?l:Pa,l(i,a)&&t.ref===e.ref)return Ze(t,e,n)}return e.flags|=1,t=Be(u,a),t.ref=e.ref,t.return=e,e.child=t}function dr(t,e,l,a,n){if(t!==null){var u=t.memoizedProps;if(Pa(u,a)&&t.ref===e.ref)if(Vt=!1,e.pendingProps=a=u,Bc(t,n))(t.flags&131072)!==0&&(Vt=!0);else return e.lanes=t.lanes,Ze(t,e,n)}return Dc(t,e,l,a,n)}function mr(t,e,l){var a=e.pendingProps,n=a.children,u=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,t!==null){for(n=e.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;e.childLanes=u&~a}else e.childLanes=0,e.child=null;return vr(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&vu(e,u!==null?u.cachePool:null),u!==null?ro(e,u):oc(),tr(e);else return e.lanes=e.childLanes=536870912,vr(t,e,u!==null?u.baseLanes|l:l,l)}else u!==null?(vu(e,u.cachePool),ro(e,u),vl(),e.memoizedState=null):(t!==null&&vu(e,null),oc(),vl());return Zt(t,e,n,l),e.child}function vr(t,e,l,a){var n=nc();return n=n===null?null:{parent:Bt._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&vu(e,null),oc(),tr(e),t!==null&&nn(t,e,a,!0),null}function Du(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(s(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function Dc(t,e,l,a,n){return Vl(e),l=mc(t,e,l,a,void 0,n),a=vc(),t!==null&&!Vt?(hc(t,e,n),Ze(t,e,n)):(ht&&a&&Wi(e),e.flags|=1,Zt(t,e,l,n),e.child)}function hr(t,e,l,a,n,u){return Vl(e),e.updateQueue=null,l=vo(e,a,l,n),mo(t),a=vc(),t!==null&&!Vt?(hc(t,e,u),Ze(t,e,u)):(ht&&a&&Wi(e),e.flags|=1,Zt(t,e,l,u),e.child)}function yr(t,e,l,a,n){if(Vl(e),e.stateNode===null){var u=va,i=l.contextType;typeof i=="object"&&i!==null&&(u=Wt(i)),u=new l(a,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Oc,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=a,u.state=e.memoizedState,u.refs={},ic(e),i=l.contextType,u.context=typeof i=="object"&&i!==null?Wt(i):va,u.state=e.memoizedState,i=l.getDerivedStateFromProps,typeof i=="function"&&(Nc(e,l,i,a),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(i=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),i!==u.state&&Oc.enqueueReplaceState(u,u.state,null),dn(e,a,u,n),rn(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){u=e.stateNode;var f=e.memoizedProps,r=Zl(l,f);u.props=r;var p=u.context,N=l.contextType;i=va,typeof N=="object"&&N!==null&&(i=Wt(N));var D=l.getDerivedStateFromProps;N=typeof D=="function"||typeof u.getSnapshotBeforeUpdate=="function",f=e.pendingProps!==f,N||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f||p!==i)&&lr(e,u,a,i),sl=!1;var x=e.memoizedState;u.state=x,dn(e,a,u,n),rn(),p=e.memoizedState,f||x!==p||sl?(typeof D=="function"&&(Nc(e,l,D,a),p=e.memoizedState),(r=sl||er(e,l,r,a,x,p,i))?(N||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=p),u.props=a,u.state=p,u.context=i,a=r):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{u=e.stateNode,cc(t,e),i=e.memoizedProps,N=Zl(l,i),u.props=N,D=e.pendingProps,x=u.context,p=l.contextType,r=va,typeof p=="object"&&p!==null&&(r=Wt(p)),f=l.getDerivedStateFromProps,(p=typeof f=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(i!==D||x!==r)&&lr(e,u,a,r),sl=!1,x=e.memoizedState,u.state=x,dn(e,a,u,n),rn();var A=e.memoizedState;i!==D||x!==A||sl||t!==null&&t.dependencies!==null&&du(t.dependencies)?(typeof f=="function"&&(Nc(e,l,f,a),A=e.memoizedState),(N=sl||er(e,l,N,a,x,A,r)||t!==null&&t.dependencies!==null&&du(t.dependencies))?(p||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,A,r),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,A,r)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&x===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&x===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=A),u.props=a,u.state=A,u.context=r,a=N):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&x===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&x===t.memoizedState||(e.flags|=1024),a=!1)}return u=a,Du(t,e),a=(e.flags&128)!==0,u||a?(u=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&a?(e.child=Ea(e,t.child,null,n),e.child=Ea(e,null,l,n)):Zt(t,e,l,n),e.memoizedState=u.state,t=e.child):t=Ze(t,e,n),t}function gr(t,e,l,a){return ln(),e.flags|=256,Zt(t,e,l,a),e.child}var Uc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function jc(t){return{baseLanes:t,cachePool:ao()}}function Cc(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=_e),t}function br(t,e,l){var a=e.pendingProps,n=!1,u=(e.flags&128)!==0,i;if((i=u)||(i=t!==null&&t.memoizedState===null?!1:(Gt.current&2)!==0),i&&(n=!0,e.flags&=-129),i=(e.flags&32)!==0,e.flags&=-33,t===null){if(ht){if(n?ml(e):vl(),ht){var f=Ut,r;if(r=f){t:{for(r=f,f=De;r.nodeType!==8;){if(!f){f=null;break t}if(r=Oe(r.nextSibling),r===null){f=null;break t}}f=r}f!==null?(e.memoizedState={dehydrated:f,treeContext:ql!==null?{id:Ge,overflow:Ye}:null,retryLane:536870912,hydrationErrors:null},r=se(18,null,null,0),r.stateNode=f,r.return=e,e.child=r,It=e,Ut=null,r=!0):r=!1}r||Yl(e)}if(f=e.memoizedState,f!==null&&(f=f.dehydrated,f!==null))return bf(f)?e.lanes=32:e.lanes=536870912,null;Le(e)}return f=a.children,a=a.fallback,n?(vl(),n=e.mode,f=Uu({mode:"hidden",children:f},n),a=Hl(a,n,l,null),f.return=e,a.return=e,f.sibling=a,e.child=f,n=e.child,n.memoizedState=jc(l),n.childLanes=Cc(t,i,l),e.memoizedState=Uc,a):(ml(e),wc(e,f))}if(r=t.memoizedState,r!==null&&(f=r.dehydrated,f!==null)){if(u)e.flags&256?(ml(e),e.flags&=-257,e=Hc(t,e,l)):e.memoizedState!==null?(vl(),e.child=t.child,e.flags|=128,e=null):(vl(),n=a.fallback,f=e.mode,a=Uu({mode:"visible",children:a.children},f),n=Hl(n,f,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Ea(e,t.child,null,l),a=e.child,a.memoizedState=jc(l),a.childLanes=Cc(t,i,l),e.memoizedState=Uc,e=n);else if(ml(e),bf(f)){if(i=f.nextSibling&&f.nextSibling.dataset,i)var p=i.dgst;i=p,a=Error(s(419)),a.stack="",a.digest=i,an({value:a,source:null,stack:null}),e=Hc(t,e,l)}else if(Vt||nn(t,e,l,!1),i=(l&t.childLanes)!==0,Vt||i){if(i=Mt,i!==null&&(a=l&-l,a=(a&42)!==0?1:bi(a),a=(a&(i.suspendedLanes|l))!==0?0:a,a!==0&&a!==r.retryLane))throw r.retryLane=a,ma(t,a),ve(i,t,a),sr;f.data==="$?"||tf(),e=Hc(t,e,l)}else f.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=r.treeContext,Ut=Oe(f.nextSibling),It=e,ht=!0,Gl=null,De=!1,t!==null&&(Te[Ae++]=Ge,Te[Ae++]=Ye,Te[Ae++]=ql,Ge=t.id,Ye=t.overflow,ql=e),e=wc(e,a.children),e.flags|=4096);return e}return n?(vl(),n=a.fallback,f=e.mode,r=t.child,p=r.sibling,a=Be(r,{mode:"hidden",children:a.children}),a.subtreeFlags=r.subtreeFlags&65011712,p!==null?n=Be(p,n):(n=Hl(n,f,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,f=t.child.memoizedState,f===null?f=jc(l):(r=f.cachePool,r!==null?(p=Bt._currentValue,r=r.parent!==p?{parent:p,pool:p}:r):r=ao(),f={baseLanes:f.baseLanes|l,cachePool:r}),n.memoizedState=f,n.childLanes=Cc(t,i,l),e.memoizedState=Uc,a):(ml(e),l=t.child,t=l.sibling,l=Be(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(i=e.deletions,i===null?(e.deletions=[t],e.flags|=16):i.push(t)),e.child=l,e.memoizedState=null,l)}function wc(t,e){return e=Uu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Uu(t,e){return t=se(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Hc(t,e,l){return Ea(e,t.child,null,l),t=wc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function pr(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),tc(t.return,e,l)}function qc(t,e,l,a,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function Sr(t,e,l){var a=e.pendingProps,n=a.revealOrder,u=a.tail;if(Zt(t,e,a.children,l),a=Gt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&pr(t,l,e);else if(t.tag===19)pr(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(w(Gt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&Nu(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),qc(e,!1,n,l,u);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&Nu(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}qc(e,!0,l,null,u);break;case"together":qc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Ze(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),pl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(nn(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(s(153));if(e.child!==null){for(t=e.child,l=Be(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=Be(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Bc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&du(t)))}function _h(t,e,l){switch(e.tag){case 3:Et(e,e.stateNode.containerInfo),fl(e,Bt,t.memoizedState.cache),ln();break;case 27:case 5:ll(e);break;case 4:Et(e,e.stateNode.containerInfo);break;case 10:fl(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(ml(e),e.flags|=128,null):(l&e.child.childLanes)!==0?br(t,e,l):(ml(e),t=Ze(t,e,l),t!==null?t.sibling:null);ml(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(nn(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return Sr(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),w(Gt,Gt.current),a)break;return null;case 22:case 23:return e.lanes=0,mr(t,e,l);case 24:fl(e,Bt,t.memoizedState.cache)}return Ze(t,e,l)}function xr(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)Vt=!0;else{if(!Bc(t,l)&&(e.flags&128)===0)return Vt=!1,_h(t,e,l);Vt=(t.flags&131072)!==0}else Vt=!1,ht&&(e.flags&1048576)!==0&&Ws(e,ru,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")ki(a)?(t=Zl(a,t),e.tag=1,e=yr(null,e,a,t,l)):(e.tag=0,e=Dc(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===ft){e.tag=11,e=or(null,e,a,t,l);break t}else if(n===bt){e.tag=14,e=rr(null,e,a,t,l);break t}}throw e=ge(a)||a,Error(s(306,e,""))}}return e;case 0:return Dc(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=Zl(a,e.pendingProps),yr(t,e,a,n,l);case 3:t:{if(Et(e,e.stateNode.containerInfo),t===null)throw Error(s(387));a=e.pendingProps;var u=e.memoizedState;n=u.element,cc(t,e),dn(e,a,null,l);var i=e.memoizedState;if(a=i.cache,fl(e,Bt,a),a!==u.cache&&ec(e,[Bt],l,!0),rn(),a=i.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:i.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=gr(t,e,a,l);break t}else if(a!==n){n=Se(Error(s(424)),e),an(n),e=gr(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ut=Oe(t.firstChild),It=e,ht=!0,Gl=null,De=!0,l=Po(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(ln(),a===n){e=Ze(t,e,l);break t}Zt(t,e,a,l)}e=e.child}return e;case 26:return Du(t,e),t===null?(l=_d(e.type,null,e.pendingProps,null))?e.memoizedState=l:ht||(l=e.type,t=e.pendingProps,a=Ku(P.current).createElement(l),a[$t]=e,a[Pt]=t,kt(a,l,t),Xt(a),e.stateNode=a):e.memoizedState=_d(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return ll(e),t===null&&ht&&(a=e.stateNode=Td(e.type,e.pendingProps,P.current),It=e,De=!0,n=Ut,Al(e.type)?(pf=n,Ut=Oe(a.firstChild)):Ut=n),Zt(t,e,e.pendingProps.children,l),Du(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&ht&&((n=a=Ut)&&(a=Ph(a,e.type,e.pendingProps,De),a!==null?(e.stateNode=a,It=e,Ut=Oe(a.firstChild),De=!1,n=!0):n=!1),n||Yl(e)),ll(e),n=e.type,u=e.pendingProps,i=t!==null?t.memoizedProps:null,a=u.children,hf(n,u)?a=null:i!==null&&hf(n,i)&&(e.flags|=32),e.memoizedState!==null&&(n=mc(t,e,gh,null,null,l),Cn._currentValue=n),Du(t,e),Zt(t,e,a,l),e.child;case 6:return t===null&&ht&&((t=l=Ut)&&(l=ty(l,e.pendingProps,De),l!==null?(e.stateNode=l,It=e,Ut=null,t=!0):t=!1),t||Yl(e)),null;case 13:return br(t,e,l);case 4:return Et(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Ea(e,null,a,l):Zt(t,e,a,l),e.child;case 11:return or(t,e,e.type,e.pendingProps,l);case 7:return Zt(t,e,e.pendingProps,l),e.child;case 8:return Zt(t,e,e.pendingProps.children,l),e.child;case 12:return Zt(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,fl(e,e.type,a.value),Zt(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,Vl(e),n=Wt(n),a=a(n),e.flags|=1,Zt(t,e,a,l),e.child;case 14:return rr(t,e,e.type,e.pendingProps,l);case 15:return dr(t,e,e.type,e.pendingProps,l);case 19:return Sr(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=Uu(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=Be(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return mr(t,e,l);case 24:return Vl(e),a=Wt(Bt),t===null?(n=nc(),n===null&&(n=Mt,u=lc(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),e.memoizedState={parent:a,cache:n},ic(e),fl(e,Bt,n)):((t.lanes&l)!==0&&(cc(t,e),dn(e,null,null,l),rn()),n=t.memoizedState,u=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),fl(e,Bt,a)):(a=u.cache,fl(e,Bt,a),a!==n.cache&&ec(e,[Bt],l,!0))),Zt(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(s(156,e.tag))}function Ke(t){t.flags|=4}function Tr(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Rd(e)){if(e=Ee.current,e!==null&&((dt&4194048)===dt?Ue!==null:(dt&62914560)!==dt&&(dt&536870912)===0||e!==Ue))throw sn=uc,no;t.flags|=8192}}function ju(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Pf():536870912,t.lanes|=e,Na|=e)}function pn(t,e){if(!ht)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Dt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function Mh(t,e,l){var a=e.pendingProps;switch(Fi(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Dt(e),null;case 1:return Dt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),Ve(Bt),ue(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(en(e)?Ke(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Ps())),Dt(e),null;case 26:return l=e.memoizedState,t===null?(Ke(e),l!==null?(Dt(e),Tr(e,l)):(Dt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(Ke(e),Dt(e),Tr(e,l)):(Dt(e),e.flags&=-16777217):(t.memoizedProps!==a&&Ke(e),Dt(e),e.flags&=-16777217),null;case 27:al(e),l=P.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&Ke(e);else{if(!a){if(e.stateNode===null)throw Error(s(166));return Dt(e),null}t=V.current,en(e)?Fs(e):(t=Td(n,a,l),e.stateNode=t,Ke(e))}return Dt(e),null;case 5:if(al(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&Ke(e);else{if(!a){if(e.stateNode===null)throw Error(s(166));return Dt(e),null}if(t=V.current,en(e))Fs(e);else{switch(n=Ku(P.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[$t]=e,t[Pt]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(kt(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Ke(e)}}return Dt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&Ke(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(s(166));if(t=P.current,en(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=It,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[$t]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||hd(t.nodeValue,l)),t||Yl(e)}else t=Ku(t).createTextNode(a),t[$t]=e,e.stateNode=t}return Dt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=en(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(s(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(s(317));n[$t]=e}else ln(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Dt(e),n=!1}else n=Ps(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(Le(e),e):(Le(e),null)}if(Le(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),ju(e,e.updateQueue),Dt(e),null;case 4:return ue(),t===null&&of(e.stateNode.containerInfo),Dt(e),null;case 10:return Ve(e.type),Dt(e),null;case 19:if(X(Gt),n=e.memoizedState,n===null)return Dt(e),null;if(a=(e.flags&128)!==0,u=n.rendering,u===null)if(a)pn(n,!1);else{if(jt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Nu(t),u!==null){for(e.flags|=128,pn(n,!1),t=u.updateQueue,e.updateQueue=t,ju(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)$s(l,t),l=l.sibling;return w(Gt,Gt.current&1|2),e.child}t=t.sibling}n.tail!==null&&Re()>Hu&&(e.flags|=128,a=!0,pn(n,!1),e.lanes=4194304)}else{if(!a)if(t=Nu(u),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,ju(e,t),pn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!ht)return Dt(e),null}else 2*Re()-n.renderingStartTime>Hu&&l!==536870912&&(e.flags|=128,a=!0,pn(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=Re(),e.sibling=null,t=Gt.current,w(Gt,a?t&1|2:t&1),e):(Dt(e),null);case 22:case 23:return Le(e),rc(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(Dt(e),e.subtreeFlags&6&&(e.flags|=8192)):Dt(e),l=e.updateQueue,l!==null&&ju(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&X(Ql),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Ve(Bt),Dt(e),null;case 25:return null;case 30:return null}throw Error(s(156,e.tag))}function zh(t,e){switch(Fi(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Ve(Bt),ue(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return al(e),null;case 13:if(Le(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(s(340));ln()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return X(Gt),null;case 4:return ue(),null;case 10:return Ve(e.type),null;case 22:case 23:return Le(e),rc(),t!==null&&X(Ql),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Ve(Bt),null;case 25:return null;default:return null}}function Ar(t,e){switch(Fi(e),e.tag){case 3:Ve(Bt),ue();break;case 26:case 27:case 5:al(e);break;case 4:ue();break;case 13:Le(e);break;case 19:X(Gt);break;case 10:Ve(e.type);break;case 22:case 23:Le(e),rc(),t!==null&&X(Ql);break;case 24:Ve(Bt)}}function Sn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var u=l.create,i=l.inst;a=u(),i.destroy=a}l=l.next}while(l!==n)}}catch(f){_t(e,e.return,f)}}function hl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&t)===t){var i=a.inst,f=i.destroy;if(f!==void 0){i.destroy=void 0,n=e;var r=l,p=f;try{p()}catch(N){_t(n,r,N)}}}a=a.next}while(a!==u)}}catch(N){_t(e,e.return,N)}}function Er(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{oo(e,l)}catch(a){_t(t,t.return,a)}}}function _r(t,e,l){l.props=Zl(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){_t(t,e,a)}}function xn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){_t(t,e,n)}}function je(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){_t(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){_t(t,e,n)}else l.current=null}function Mr(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){_t(t,t.return,n)}}function Gc(t,e,l){try{var a=t.stateNode;Jh(a,t.type,l,e),a[Pt]=e}catch(n){_t(t,t.return,n)}}function zr(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Al(t.type)||t.tag===4}function Yc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||zr(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Al(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Xc(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=Zu));else if(a!==4&&(a===27&&Al(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(Xc(t,e,l),t=t.sibling;t!==null;)Xc(t,e,l),t=t.sibling}function Cu(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&Al(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(Cu(t,e,l),t=t.sibling;t!==null;)Cu(t,e,l),t=t.sibling}function Nr(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);kt(e,a,l),e[$t]=t,e[Pt]=l}catch(u){_t(t,t.return,u)}}var ke=!1,wt=!1,Vc=!1,Or=typeof WeakSet=="function"?WeakSet:Set,Qt=null;function Nh(t,e){if(t=t.containerInfo,mf=Iu,t=Gs(t),Yi(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var i=0,f=-1,r=-1,p=0,N=0,D=t,x=null;e:for(;;){for(var A;D!==l||n!==0&&D.nodeType!==3||(f=i+n),D!==u||a!==0&&D.nodeType!==3||(r=i+a),D.nodeType===3&&(i+=D.nodeValue.length),(A=D.firstChild)!==null;)x=D,D=A;for(;;){if(D===t)break e;if(x===l&&++p===n&&(f=i),x===u&&++N===a&&(r=i),(A=D.nextSibling)!==null)break;D=x,x=D.parentNode}D=A}l=f===-1||r===-1?null:{start:f,end:r}}else l=null}l=l||{start:0,end:0}}else l=null;for(vf={focusedElem:t,selectionRange:l},Iu=!1,Qt=e;Qt!==null;)if(e=Qt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Qt=t;else for(;Qt!==null;){switch(e=Qt,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var I=Zl(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(I,u),a.__reactInternalSnapshotBeforeUpdate=t}catch(J){_t(l,l.return,J)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)gf(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":gf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(s(163))}if(t=e.sibling,t!==null){t.return=e.return,Qt=t;break}Qt=e.return}}function Rr(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:yl(t,l),a&4&&Sn(5,l);break;case 1:if(yl(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(i){_t(l,l.return,i)}else{var n=Zl(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(i){_t(l,l.return,i)}}a&64&&Er(l),a&512&&xn(l,l.return);break;case 3:if(yl(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{oo(t,e)}catch(i){_t(l,l.return,i)}}break;case 27:e===null&&a&4&&Nr(l);case 26:case 5:yl(t,l),e===null&&a&4&&Mr(l),a&512&&xn(l,l.return);break;case 12:yl(t,l);break;case 13:yl(t,l),a&4&&jr(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=qh.bind(null,l),ey(t,l))));break;case 22:if(a=l.memoizedState!==null||ke,!a){e=e!==null&&e.memoizedState!==null||wt,n=ke;var u=wt;ke=a,(wt=e)&&!u?gl(t,l,(l.subtreeFlags&8772)!==0):yl(t,l),ke=n,wt=u}break;case 30:break;default:yl(t,l)}}function Dr(t){var e=t.alternate;e!==null&&(t.alternate=null,Dr(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&xi(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Ot=null,le=!1;function Je(t,e,l){for(l=l.child;l!==null;)Ur(t,e,l),l=l.sibling}function Ur(t,e,l){if(ie&&typeof ie.onCommitFiberUnmount=="function")try{ie.onCommitFiberUnmount(Xa,l)}catch{}switch(l.tag){case 26:wt||je(l,e),Je(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:wt||je(l,e);var a=Ot,n=le;Al(l.type)&&(Ot=l.stateNode,le=!1),Je(t,e,l),Rn(l.stateNode),Ot=a,le=n;break;case 5:wt||je(l,e);case 6:if(a=Ot,n=le,Ot=null,Je(t,e,l),Ot=a,le=n,Ot!==null)if(le)try{(Ot.nodeType===9?Ot.body:Ot.nodeName==="HTML"?Ot.ownerDocument.body:Ot).removeChild(l.stateNode)}catch(u){_t(l,e,u)}else try{Ot.removeChild(l.stateNode)}catch(u){_t(l,e,u)}break;case 18:Ot!==null&&(le?(t=Ot,Sd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),Bn(t)):Sd(Ot,l.stateNode));break;case 4:a=Ot,n=le,Ot=l.stateNode.containerInfo,le=!0,Je(t,e,l),Ot=a,le=n;break;case 0:case 11:case 14:case 15:wt||hl(2,l,e),wt||hl(4,l,e),Je(t,e,l);break;case 1:wt||(je(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&_r(l,e,a)),Je(t,e,l);break;case 21:Je(t,e,l);break;case 22:wt=(a=wt)||l.memoizedState!==null,Je(t,e,l),wt=a;break;default:Je(t,e,l)}}function jr(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Bn(t)}catch(l){_t(e,e.return,l)}}function Oh(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Or),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Or),e;default:throw Error(s(435,t.tag))}}function Qc(t,e){var l=Oh(t);e.forEach(function(a){var n=Bh.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function oe(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=t,i=e,f=i;t:for(;f!==null;){switch(f.tag){case 27:if(Al(f.type)){Ot=f.stateNode,le=!1;break t}break;case 5:Ot=f.stateNode,le=!1;break t;case 3:case 4:Ot=f.stateNode.containerInfo,le=!0;break t}f=f.return}if(Ot===null)throw Error(s(160));Ur(u,i,n),Ot=null,le=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Cr(e,t),e=e.sibling}var Ne=null;function Cr(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:oe(e,t),re(t),a&4&&(hl(3,t,t.return),Sn(3,t),hl(5,t,t.return));break;case 1:oe(e,t),re(t),a&512&&(wt||l===null||je(l,l.return)),a&64&&ke&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Ne;if(oe(e,t),re(t),a&512&&(wt||l===null||je(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[La]||u[$t]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),kt(u,a,l),u[$t]=t,Xt(u),a=u;break t;case"link":var i=Nd("link","href",n).get(a+(l.href||""));if(i){for(var f=0;f<i.length;f++)if(u=i[f],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){i.splice(f,1);break e}}u=n.createElement(a),kt(u,a,l),n.head.appendChild(u);break;case"meta":if(i=Nd("meta","content",n).get(a+(l.content||""))){for(f=0;f<i.length;f++)if(u=i[f],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){i.splice(f,1);break e}}u=n.createElement(a),kt(u,a,l),n.head.appendChild(u);break;default:throw Error(s(468,a))}u[$t]=t,Xt(u),a=u}t.stateNode=a}else Od(n,t.type,t.stateNode);else t.stateNode=zd(n,a,t.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?Od(n,t.type,t.stateNode):zd(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&Gc(t,t.memoizedProps,l.memoizedProps)}break;case 27:oe(e,t),re(t),a&512&&(wt||l===null||je(l,l.return)),l!==null&&a&4&&Gc(t,t.memoizedProps,l.memoizedProps);break;case 5:if(oe(e,t),re(t),a&512&&(wt||l===null||je(l,l.return)),t.flags&32){n=t.stateNode;try{ia(n,"")}catch(A){_t(t,t.return,A)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,Gc(t,n,l!==null?l.memoizedProps:n)),a&1024&&(Vc=!0);break;case 6:if(oe(e,t),re(t),a&4){if(t.stateNode===null)throw Error(s(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(A){_t(t,t.return,A)}}break;case 3:if($u=null,n=Ne,Ne=ku(e.containerInfo),oe(e,t),Ne=n,re(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Bn(e.containerInfo)}catch(A){_t(t,t.return,A)}Vc&&(Vc=!1,wr(t));break;case 4:a=Ne,Ne=ku(t.stateNode.containerInfo),oe(e,t),re(t),Ne=a;break;case 12:oe(e,t),re(t);break;case 13:oe(e,t),re(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&($c=Re()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Qc(t,a)));break;case 22:n=t.memoizedState!==null;var r=l!==null&&l.memoizedState!==null,p=ke,N=wt;if(ke=p||n,wt=N||r,oe(e,t),wt=N,ke=p,re(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||r||ke||wt||Kl(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){r=l=e;try{if(u=r.stateNode,n)i=u.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none";else{f=r.stateNode;var D=r.memoizedProps.style,x=D!=null&&D.hasOwnProperty("display")?D.display:null;f.style.display=x==null||typeof x=="boolean"?"":(""+x).trim()}}catch(A){_t(r,r.return,A)}}}else if(e.tag===6){if(l===null){r=e;try{r.stateNode.nodeValue=n?"":r.memoizedProps}catch(A){_t(r,r.return,A)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Qc(t,l))));break;case 19:oe(e,t),re(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Qc(t,a)));break;case 30:break;case 21:break;default:oe(e,t),re(t)}}function re(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(zr(a)){l=a;break}a=a.return}if(l==null)throw Error(s(160));switch(l.tag){case 27:var n=l.stateNode,u=Yc(t);Cu(t,u,n);break;case 5:var i=l.stateNode;l.flags&32&&(ia(i,""),l.flags&=-33);var f=Yc(t);Cu(t,f,i);break;case 3:case 4:var r=l.stateNode.containerInfo,p=Yc(t);Xc(t,p,r);break;default:throw Error(s(161))}}catch(N){_t(t,t.return,N)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function wr(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;wr(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function yl(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Rr(t,e.alternate,e),e=e.sibling}function Kl(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:hl(4,e,e.return),Kl(e);break;case 1:je(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&_r(e,e.return,l),Kl(e);break;case 27:Rn(e.stateNode);case 26:case 5:je(e,e.return),Kl(e);break;case 22:e.memoizedState===null&&Kl(e);break;case 30:Kl(e);break;default:Kl(e)}t=t.sibling}}function gl(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,u=e,i=u.flags;switch(u.tag){case 0:case 11:case 15:gl(n,u,l),Sn(4,u);break;case 1:if(gl(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(p){_t(a,a.return,p)}if(a=u,n=a.updateQueue,n!==null){var f=a.stateNode;try{var r=n.shared.hiddenCallbacks;if(r!==null)for(n.shared.hiddenCallbacks=null,n=0;n<r.length;n++)so(r[n],f)}catch(p){_t(a,a.return,p)}}l&&i&64&&Er(u),xn(u,u.return);break;case 27:Nr(u);case 26:case 5:gl(n,u,l),l&&a===null&&i&4&&Mr(u),xn(u,u.return);break;case 12:gl(n,u,l);break;case 13:gl(n,u,l),l&&i&4&&jr(n,u);break;case 22:u.memoizedState===null&&gl(n,u,l),xn(u,u.return);break;case 30:break;default:gl(n,u,l)}e=e.sibling}}function Lc(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&un(l))}function Zc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t))}function Ce(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Hr(t,e,l,a),e=e.sibling}function Hr(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:Ce(t,e,l,a),n&2048&&Sn(9,e);break;case 1:Ce(t,e,l,a);break;case 3:Ce(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t)));break;case 12:if(n&2048){Ce(t,e,l,a),t=e.stateNode;try{var u=e.memoizedProps,i=u.id,f=u.onPostCommit;typeof f=="function"&&f(i,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(r){_t(e,e.return,r)}}else Ce(t,e,l,a);break;case 13:Ce(t,e,l,a);break;case 23:break;case 22:u=e.stateNode,i=e.alternate,e.memoizedState!==null?u._visibility&2?Ce(t,e,l,a):Tn(t,e):u._visibility&2?Ce(t,e,l,a):(u._visibility|=2,_a(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Lc(i,e);break;case 24:Ce(t,e,l,a),n&2048&&Zc(e.alternate,e);break;default:Ce(t,e,l,a)}}function _a(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,i=e,f=l,r=a,p=i.flags;switch(i.tag){case 0:case 11:case 15:_a(u,i,f,r,n),Sn(8,i);break;case 23:break;case 22:var N=i.stateNode;i.memoizedState!==null?N._visibility&2?_a(u,i,f,r,n):Tn(u,i):(N._visibility|=2,_a(u,i,f,r,n)),n&&p&2048&&Lc(i.alternate,i);break;case 24:_a(u,i,f,r,n),n&&p&2048&&Zc(i.alternate,i);break;default:_a(u,i,f,r,n)}e=e.sibling}}function Tn(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:Tn(l,a),n&2048&&Lc(a.alternate,a);break;case 24:Tn(l,a),n&2048&&Zc(a.alternate,a);break;default:Tn(l,a)}e=e.sibling}}var An=8192;function Ma(t){if(t.subtreeFlags&An)for(t=t.child;t!==null;)qr(t),t=t.sibling}function qr(t){switch(t.tag){case 26:Ma(t),t.flags&An&&t.memoizedState!==null&&vy(Ne,t.memoizedState,t.memoizedProps);break;case 5:Ma(t);break;case 3:case 4:var e=Ne;Ne=ku(t.stateNode.containerInfo),Ma(t),Ne=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=An,An=16777216,Ma(t),An=e):Ma(t));break;default:Ma(t)}}function Br(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function En(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Qt=a,Yr(a,t)}Br(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Gr(t),t=t.sibling}function Gr(t){switch(t.tag){case 0:case 11:case 15:En(t),t.flags&2048&&hl(9,t,t.return);break;case 3:En(t);break;case 12:En(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,wu(t)):En(t);break;default:En(t)}}function wu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Qt=a,Yr(a,t)}Br(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:hl(8,e,e.return),wu(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,wu(e));break;default:wu(e)}t=t.sibling}}function Yr(t,e){for(;Qt!==null;){var l=Qt;switch(l.tag){case 0:case 11:case 15:hl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:un(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Qt=a;else t:for(l=t;Qt!==null;){a=Qt;var n=a.sibling,u=a.return;if(Dr(a),a===l){Qt=null;break t}if(n!==null){n.return=u,Qt=n;break t}Qt=u}}}var Rh={getCacheForType:function(t){var e=Wt(Bt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},Dh=typeof WeakMap=="function"?WeakMap:Map,pt=0,Mt=null,ot=null,dt=0,St=0,de=null,bl=!1,za=!1,Kc=!1,$e=0,jt=0,pl=0,kl=0,kc=0,_e=0,Na=0,_n=null,ae=null,Jc=!1,$c=0,Hu=1/0,qu=null,Sl=null,Kt=0,xl=null,Oa=null,Ra=0,Wc=0,Fc=null,Xr=null,Mn=0,Ic=null;function me(){if((pt&2)!==0&&dt!==0)return dt&-dt;if(_.T!==null){var t=ga;return t!==0?t:uf()}return ls()}function Vr(){_e===0&&(_e=(dt&536870912)===0||ht?If():536870912);var t=Ee.current;return t!==null&&(t.flags|=32),_e}function ve(t,e,l){(t===Mt&&(St===2||St===9)||t.cancelPendingCommit!==null)&&(Da(t,0),Tl(t,dt,_e,!1)),Qa(t,l),((pt&2)===0||t!==Mt)&&(t===Mt&&((pt&2)===0&&(kl|=l),jt===4&&Tl(t,dt,_e,!1)),we(t))}function Qr(t,e,l){if((pt&6)!==0)throw Error(s(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||Va(t,e),n=a?Ch(t,e):ef(t,e,!0),u=a;do{if(n===0){za&&!a&&Tl(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!Uh(l)){n=ef(t,e,!1),u=!1;continue}if(n===2){if(u=e,t.errorRecoveryDisabledLanes&u)var i=0;else i=t.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){e=i;t:{var f=t;n=_n;var r=f.current.memoizedState.isDehydrated;if(r&&(Da(f,i).flags|=256),i=ef(f,i,!1),i!==2){if(Kc&&!r){f.errorRecoveryDisabledLanes|=u,kl|=u,n=4;break t}u=ae,ae=n,u!==null&&(ae===null?ae=u:ae.push.apply(ae,u))}n=i}if(u=!1,n!==2)continue}}if(n===1){Da(t,0),Tl(t,e,0,!0);break}t:{switch(a=t,u=n,u){case 0:case 1:throw Error(s(345));case 4:if((e&4194048)!==e)break;case 6:Tl(a,e,_e,!bl);break t;case 2:ae=null;break;case 3:case 5:break;default:throw Error(s(329))}if((e&62914560)===e&&(n=$c+300-Re(),10<n)){if(Tl(a,e,_e,!bl),Jn(a,0,!0)!==0)break t;a.timeoutHandle=bd(Lr.bind(null,a,l,ae,qu,Jc,e,_e,kl,Na,bl,u,2,-0,0),n);break t}Lr(a,l,ae,qu,Jc,e,_e,kl,Na,bl,u,0,-0,0)}}break}while(!0);we(t)}function Lr(t,e,l,a,n,u,i,f,r,p,N,D,x,A){if(t.timeoutHandle=-1,D=e.subtreeFlags,(D&8192||(D&16785408)===16785408)&&(jn={stylesheets:null,count:0,unsuspend:my},qr(e),D=hy(),D!==null)){t.cancelPendingCommit=D(Fr.bind(null,t,e,u,l,a,n,i,f,r,N,1,x,A)),Tl(t,u,i,!p);return}Fr(t,e,u,l,a,n,i,f,r)}function Uh(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!fe(u(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Tl(t,e,l,a){e&=~kc,e&=~kl,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var u=31-ce(n),i=1<<u;a[u]=-1,n&=~i}l!==0&&ts(t,l,e)}function Bu(){return(pt&6)===0?(zn(0),!1):!0}function Pc(){if(ot!==null){if(St===0)var t=ot.return;else t=ot,Xe=Xl=null,yc(t),Aa=null,gn=0,t=ot;for(;t!==null;)Ar(t.alternate,t),t=t.return;ot=null}}function Da(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,Wh(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Pc(),Mt=t,ot=l=Be(t.current,null),dt=e,St=0,de=null,bl=!1,za=Va(t,e),Kc=!1,Na=_e=kc=kl=pl=jt=0,ae=_n=null,Jc=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-ce(a),u=1<<n;e|=t[n],a&=~u}return $e=e,iu(),l}function Zr(t,e){nt=null,_.H=_u,e===fn||e===hu?(e=co(),St=3):e===no?(e=co(),St=4):St=e===sr?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,de=e,ot===null&&(jt=1,Ru(t,Se(e,t.current)))}function Kr(){var t=_.H;return _.H=_u,t===null?_u:t}function kr(){var t=_.A;return _.A=Rh,t}function tf(){jt=4,bl||(dt&4194048)!==dt&&Ee.current!==null||(za=!0),(pl&134217727)===0&&(kl&134217727)===0||Mt===null||Tl(Mt,dt,_e,!1)}function ef(t,e,l){var a=pt;pt|=2;var n=Kr(),u=kr();(Mt!==t||dt!==e)&&(qu=null,Da(t,e)),e=!1;var i=jt;t:do try{if(St!==0&&ot!==null){var f=ot,r=de;switch(St){case 8:Pc(),i=6;break t;case 3:case 2:case 9:case 6:Ee.current===null&&(e=!0);var p=St;if(St=0,de=null,Ua(t,f,r,p),l&&za){i=0;break t}break;default:p=St,St=0,de=null,Ua(t,f,r,p)}}jh(),i=jt;break}catch(N){Zr(t,N)}while(!0);return e&&t.shellSuspendCounter++,Xe=Xl=null,pt=a,_.H=n,_.A=u,ot===null&&(Mt=null,dt=0,iu()),i}function jh(){for(;ot!==null;)Jr(ot)}function Ch(t,e){var l=pt;pt|=2;var a=Kr(),n=kr();Mt!==t||dt!==e?(qu=null,Hu=Re()+500,Da(t,e)):za=Va(t,e);t:do try{if(St!==0&&ot!==null){e=ot;var u=de;e:switch(St){case 1:St=0,de=null,Ua(t,e,u,1);break;case 2:case 9:if(uo(u)){St=0,de=null,$r(e);break}e=function(){St!==2&&St!==9||Mt!==t||(St=7),we(t)},u.then(e,e);break t;case 3:St=7;break t;case 4:St=5;break t;case 7:uo(u)?(St=0,de=null,$r(e)):(St=0,de=null,Ua(t,e,u,7));break;case 5:var i=null;switch(ot.tag){case 26:i=ot.memoizedState;case 5:case 27:var f=ot;if(!i||Rd(i)){St=0,de=null;var r=f.sibling;if(r!==null)ot=r;else{var p=f.return;p!==null?(ot=p,Gu(p)):ot=null}break e}}St=0,de=null,Ua(t,e,u,5);break;case 6:St=0,de=null,Ua(t,e,u,6);break;case 8:Pc(),jt=6;break t;default:throw Error(s(462))}}wh();break}catch(N){Zr(t,N)}while(!0);return Xe=Xl=null,_.H=a,_.A=n,pt=l,ot!==null?0:(Mt=null,dt=0,iu(),jt)}function wh(){for(;ot!==null&&!av();)Jr(ot)}function Jr(t){var e=xr(t.alternate,t,$e);t.memoizedProps=t.pendingProps,e===null?Gu(t):ot=e}function $r(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=hr(l,e,e.pendingProps,e.type,void 0,dt);break;case 11:e=hr(l,e,e.pendingProps,e.type.render,e.ref,dt);break;case 5:yc(e);default:Ar(l,e),e=ot=$s(e,$e),e=xr(l,e,$e)}t.memoizedProps=t.pendingProps,e===null?Gu(t):ot=e}function Ua(t,e,l,a){Xe=Xl=null,yc(e),Aa=null,gn=0;var n=e.return;try{if(Eh(t,n,e,l,dt)){jt=1,Ru(t,Se(l,t.current)),ot=null;return}}catch(u){if(n!==null)throw ot=n,u;jt=1,Ru(t,Se(l,t.current)),ot=null;return}e.flags&32768?(ht||a===1?t=!0:za||(dt&536870912)!==0?t=!1:(bl=t=!0,(a===2||a===9||a===3||a===6)&&(a=Ee.current,a!==null&&a.tag===13&&(a.flags|=16384))),Wr(e,t)):Gu(e)}function Gu(t){var e=t;do{if((e.flags&32768)!==0){Wr(e,bl);return}t=e.return;var l=Mh(e.alternate,e,$e);if(l!==null){ot=l;return}if(e=e.sibling,e!==null){ot=e;return}ot=e=t}while(e!==null);jt===0&&(jt=5)}function Wr(t,e){do{var l=zh(t.alternate,t);if(l!==null){l.flags&=32767,ot=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){ot=t;return}ot=t=l}while(t!==null);jt=6,ot=null}function Fr(t,e,l,a,n,u,i,f,r){t.cancelPendingCommit=null;do Yu();while(Kt!==0);if((pt&6)!==0)throw Error(s(327));if(e!==null){if(e===t.current)throw Error(s(177));if(u=e.lanes|e.childLanes,u|=Zi,mv(t,l,u,i,f,r),t===Mt&&(ot=Mt=null,dt=0),Oa=e,xl=t,Ra=l,Wc=u,Fc=n,Xr=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Gh(Zn,function(){return ld(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=_.T,_.T=null,n=B.p,B.p=2,i=pt,pt|=4;try{Nh(t,e,l)}finally{pt=i,B.p=n,_.T=a}}Kt=1,Ir(),Pr(),td()}}function Ir(){if(Kt===1){Kt=0;var t=xl,e=Oa,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=_.T,_.T=null;var a=B.p;B.p=2;var n=pt;pt|=4;try{Cr(e,t);var u=vf,i=Gs(t.containerInfo),f=u.focusedElem,r=u.selectionRange;if(i!==f&&f&&f.ownerDocument&&Bs(f.ownerDocument.documentElement,f)){if(r!==null&&Yi(f)){var p=r.start,N=r.end;if(N===void 0&&(N=p),"selectionStart"in f)f.selectionStart=p,f.selectionEnd=Math.min(N,f.value.length);else{var D=f.ownerDocument||document,x=D&&D.defaultView||window;if(x.getSelection){var A=x.getSelection(),I=f.textContent.length,J=Math.min(r.start,I),At=r.end===void 0?J:Math.min(r.end,I);!A.extend&&J>At&&(i=At,At=J,J=i);var y=qs(f,J),v=qs(f,At);if(y&&v&&(A.rangeCount!==1||A.anchorNode!==y.node||A.anchorOffset!==y.offset||A.focusNode!==v.node||A.focusOffset!==v.offset)){var b=D.createRange();b.setStart(y.node,y.offset),A.removeAllRanges(),J>At?(A.addRange(b),A.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),A.addRange(b))}}}}for(D=[],A=f;A=A.parentNode;)A.nodeType===1&&D.push({element:A,left:A.scrollLeft,top:A.scrollTop});for(typeof f.focus=="function"&&f.focus(),f=0;f<D.length;f++){var O=D[f];O.element.scrollLeft=O.left,O.element.scrollTop=O.top}}Iu=!!mf,vf=mf=null}finally{pt=n,B.p=a,_.T=l}}t.current=e,Kt=2}}function Pr(){if(Kt===2){Kt=0;var t=xl,e=Oa,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=_.T,_.T=null;var a=B.p;B.p=2;var n=pt;pt|=4;try{Rr(t,e.alternate,e)}finally{pt=n,B.p=a,_.T=l}}Kt=3}}function td(){if(Kt===4||Kt===3){Kt=0,nv();var t=xl,e=Oa,l=Ra,a=Xr;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Kt=5:(Kt=0,Oa=xl=null,ed(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(Sl=null),pi(l),e=e.stateNode,ie&&typeof ie.onCommitFiberRoot=="function")try{ie.onCommitFiberRoot(Xa,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=_.T,n=B.p,B.p=2,_.T=null;try{for(var u=t.onRecoverableError,i=0;i<a.length;i++){var f=a[i];u(f.value,{componentStack:f.stack})}}finally{_.T=e,B.p=n}}(Ra&3)!==0&&Yu(),we(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===Ic?Mn++:(Mn=0,Ic=t):Mn=0,zn(0)}}function ed(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,un(e)))}function Yu(t){return Ir(),Pr(),td(),ld()}function ld(){if(Kt!==5)return!1;var t=xl,e=Wc;Wc=0;var l=pi(Ra),a=_.T,n=B.p;try{B.p=32>l?32:l,_.T=null,l=Fc,Fc=null;var u=xl,i=Ra;if(Kt=0,Oa=xl=null,Ra=0,(pt&6)!==0)throw Error(s(331));var f=pt;if(pt|=4,Gr(u.current),Hr(u,u.current,i,l),pt=f,zn(0,!1),ie&&typeof ie.onPostCommitFiberRoot=="function")try{ie.onPostCommitFiberRoot(Xa,u)}catch{}return!0}finally{B.p=n,_.T=a,ed(t,e)}}function ad(t,e,l){e=Se(l,e),e=Rc(t.stateNode,e,2),t=rl(t,e,2),t!==null&&(Qa(t,2),we(t))}function _t(t,e,l){if(t.tag===3)ad(t,t,l);else for(;e!==null;){if(e.tag===3){ad(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Sl===null||!Sl.has(a))){t=Se(l,t),l=cr(2),a=rl(e,l,2),a!==null&&(fr(l,a,e,t),Qa(a,2),we(a));break}}e=e.return}}function lf(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new Dh;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Kc=!0,n.add(l),t=Hh.bind(null,t,e,l),e.then(t,t))}function Hh(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Mt===t&&(dt&l)===l&&(jt===4||jt===3&&(dt&62914560)===dt&&300>Re()-$c?(pt&2)===0&&Da(t,0):kc|=l,Na===dt&&(Na=0)),we(t)}function nd(t,e){e===0&&(e=Pf()),t=ma(t,e),t!==null&&(Qa(t,e),we(t))}function qh(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),nd(t,l)}function Bh(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(s(314))}a!==null&&a.delete(e),nd(t,l)}function Gh(t,e){return hi(t,e)}var Xu=null,ja=null,af=!1,Vu=!1,nf=!1,Jl=0;function we(t){t!==ja&&t.next===null&&(ja===null?Xu=ja=t:ja=ja.next=t),Vu=!0,af||(af=!0,Xh())}function zn(t,e){if(!nf&&Vu){nf=!0;do for(var l=!1,a=Xu;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var i=a.suspendedLanes,f=a.pingedLanes;u=(1<<31-ce(42|t)+1)-1,u&=n&~(i&~f),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,fd(a,u))}else u=dt,u=Jn(a,a===Mt?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||Va(a,u)||(l=!0,fd(a,u));a=a.next}while(l);nf=!1}}function Yh(){ud()}function ud(){Vu=af=!1;var t=0;Jl!==0&&($h()&&(t=Jl),Jl=0);for(var e=Re(),l=null,a=Xu;a!==null;){var n=a.next,u=id(a,e);u===0?(a.next=null,l===null?Xu=n:l.next=n,n===null&&(ja=l)):(l=a,(t!==0||(u&3)!==0)&&(Vu=!0)),a=n}zn(t)}function id(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var i=31-ce(u),f=1<<i,r=n[i];r===-1?((f&l)===0||(f&a)!==0)&&(n[i]=dv(f,e)):r<=e&&(t.expiredLanes|=f),u&=~f}if(e=Mt,l=dt,l=Jn(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(St===2||St===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&yi(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||Va(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&yi(a),pi(l)){case 2:case 8:l=Wf;break;case 32:l=Zn;break;case 268435456:l=Ff;break;default:l=Zn}return a=cd.bind(null,t),l=hi(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&yi(a),t.callbackPriority=2,t.callbackNode=null,2}function cd(t,e){if(Kt!==0&&Kt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(Yu()&&t.callbackNode!==l)return null;var a=dt;return a=Jn(t,t===Mt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(Qr(t,a,e),id(t,Re()),t.callbackNode!=null&&t.callbackNode===l?cd.bind(null,t):null)}function fd(t,e){if(Yu())return null;Qr(t,e,!0)}function Xh(){Fh(function(){(pt&6)!==0?hi($f,Yh):ud()})}function uf(){return Jl===0&&(Jl=If()),Jl}function sd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Pn(""+t)}function od(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Vh(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var u=sd((n[Pt]||null).action),i=a.submitter;i&&(e=(e=i[Pt]||null)?sd(e.formAction):i.getAttribute("formAction"),e!==null&&(u=e,i=null));var f=new au("action","action",null,a,n);t.push({event:f,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Jl!==0){var r=i?od(n,i):new FormData(n);_c(l,{pending:!0,data:r,method:n.method,action:u},null,r)}}else typeof u=="function"&&(f.preventDefault(),r=i?od(n,i):new FormData(n),_c(l,{pending:!0,data:r,method:n.method,action:u},u,r))},currentTarget:n}]})}}for(var cf=0;cf<Li.length;cf++){var ff=Li[cf],Qh=ff.toLowerCase(),Lh=ff[0].toUpperCase()+ff.slice(1);ze(Qh,"on"+Lh)}ze(Vs,"onAnimationEnd"),ze(Qs,"onAnimationIteration"),ze(Ls,"onAnimationStart"),ze("dblclick","onDoubleClick"),ze("focusin","onFocus"),ze("focusout","onBlur"),ze(ch,"onTransitionRun"),ze(fh,"onTransitionStart"),ze(sh,"onTransitionCancel"),ze(Zs,"onTransitionEnd"),aa("onMouseEnter",["mouseout","mouseover"]),aa("onMouseLeave",["mouseout","mouseover"]),aa("onPointerEnter",["pointerout","pointerover"]),aa("onPointerLeave",["pointerout","pointerover"]),Ul("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ul("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ul("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ul("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ul("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ul("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Nn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Zh=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Nn));function rd(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var u=void 0;if(e)for(var i=a.length-1;0<=i;i--){var f=a[i],r=f.instance,p=f.currentTarget;if(f=f.listener,r!==u&&n.isPropagationStopped())break t;u=f,n.currentTarget=p;try{u(n)}catch(N){Ou(N)}n.currentTarget=null,u=r}else for(i=0;i<a.length;i++){if(f=a[i],r=f.instance,p=f.currentTarget,f=f.listener,r!==u&&n.isPropagationStopped())break t;u=f,n.currentTarget=p;try{u(n)}catch(N){Ou(N)}n.currentTarget=null,u=r}}}}function rt(t,e){var l=e[Si];l===void 0&&(l=e[Si]=new Set);var a=t+"__bubble";l.has(a)||(dd(e,t,2,!1),l.add(a))}function sf(t,e,l){var a=0;e&&(a|=4),dd(l,t,a,e)}var Qu="_reactListening"+Math.random().toString(36).slice(2);function of(t){if(!t[Qu]){t[Qu]=!0,ns.forEach(function(l){l!=="selectionchange"&&(Zh.has(l)||sf(l,!1,t),sf(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Qu]||(e[Qu]=!0,sf("selectionchange",!1,e))}}function dd(t,e,l,a){switch(Hd(e)){case 2:var n=by;break;case 8:n=py;break;default:n=Ef}l=n.bind(null,e,l,t),n=void 0,!Di||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function rf(t,e,l,a,n){var u=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var f=a.stateNode.containerInfo;if(f===n)break;if(i===4)for(i=a.return;i!==null;){var r=i.tag;if((r===3||r===4)&&i.stateNode.containerInfo===n)return;i=i.return}for(;f!==null;){if(i=ta(f),i===null)return;if(r=i.tag,r===5||r===6||r===26||r===27){a=u=i;continue t}f=f.parentNode}}a=a.return}bs(function(){var p=u,N=Oi(l),D=[];t:{var x=Ks.get(t);if(x!==void 0){var A=au,I=t;switch(t){case"keypress":if(eu(l)===0)break t;case"keydown":case"keyup":A=Gv;break;case"focusin":I="focus",A=wi;break;case"focusout":I="blur",A=wi;break;case"beforeblur":case"afterblur":A=wi;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":A=xs;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":A=zv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":A=Vv;break;case Vs:case Qs:case Ls:A=Rv;break;case Zs:A=Lv;break;case"scroll":case"scrollend":A=_v;break;case"wheel":A=Kv;break;case"copy":case"cut":case"paste":A=Uv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":A=As;break;case"toggle":case"beforetoggle":A=Jv}var J=(e&4)!==0,At=!J&&(t==="scroll"||t==="scrollend"),y=J?x!==null?x+"Capture":null:x;J=[];for(var v=p,b;v!==null;){var O=v;if(b=O.stateNode,O=O.tag,O!==5&&O!==26&&O!==27||b===null||y===null||(O=Ka(v,y),O!=null&&J.push(On(v,O,b))),At)break;v=v.return}0<J.length&&(x=new A(x,I,null,l,N),D.push({event:x,listeners:J}))}}if((e&7)===0){t:{if(x=t==="mouseover"||t==="pointerover",A=t==="mouseout"||t==="pointerout",x&&l!==Ni&&(I=l.relatedTarget||l.fromElement)&&(ta(I)||I[Pl]))break t;if((A||x)&&(x=N.window===N?N:(x=N.ownerDocument)?x.defaultView||x.parentWindow:window,A?(I=l.relatedTarget||l.toElement,A=p,I=I?ta(I):null,I!==null&&(At=T(I),J=I.tag,I!==At||J!==5&&J!==27&&J!==6)&&(I=null)):(A=null,I=p),A!==I)){if(J=xs,O="onMouseLeave",y="onMouseEnter",v="mouse",(t==="pointerout"||t==="pointerover")&&(J=As,O="onPointerLeave",y="onPointerEnter",v="pointer"),At=A==null?x:Za(A),b=I==null?x:Za(I),x=new J(O,v+"leave",A,l,N),x.target=At,x.relatedTarget=b,O=null,ta(N)===p&&(J=new J(y,v+"enter",I,l,N),J.target=b,J.relatedTarget=At,O=J),At=O,A&&I)e:{for(J=A,y=I,v=0,b=J;b;b=Ca(b))v++;for(b=0,O=y;O;O=Ca(O))b++;for(;0<v-b;)J=Ca(J),v--;for(;0<b-v;)y=Ca(y),b--;for(;v--;){if(J===y||y!==null&&J===y.alternate)break e;J=Ca(J),y=Ca(y)}J=null}else J=null;A!==null&&md(D,x,A,J,!1),I!==null&&At!==null&&md(D,At,I,J,!0)}}t:{if(x=p?Za(p):window,A=x.nodeName&&x.nodeName.toLowerCase(),A==="select"||A==="input"&&x.type==="file")var Q=Ds;else if(Os(x))if(Us)Q=nh;else{Q=lh;var it=eh}else A=x.nodeName,!A||A.toLowerCase()!=="input"||x.type!=="checkbox"&&x.type!=="radio"?p&&zi(p.elementType)&&(Q=Ds):Q=ah;if(Q&&(Q=Q(t,p))){Rs(D,Q,l,N);break t}it&&it(t,x,p),t==="focusout"&&p&&x.type==="number"&&p.memoizedProps.value!=null&&Mi(x,"number",x.value)}switch(it=p?Za(p):window,t){case"focusin":(Os(it)||it.contentEditable==="true")&&(oa=it,Xi=p,tn=null);break;case"focusout":tn=Xi=oa=null;break;case"mousedown":Vi=!0;break;case"contextmenu":case"mouseup":case"dragend":Vi=!1,Ys(D,l,N);break;case"selectionchange":if(ih)break;case"keydown":case"keyup":Ys(D,l,N)}var k;if(qi)t:{switch(t){case"compositionstart":var W="onCompositionStart";break t;case"compositionend":W="onCompositionEnd";break t;case"compositionupdate":W="onCompositionUpdate";break t}W=void 0}else sa?zs(t,l)&&(W="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(W="onCompositionStart");W&&(Es&&l.locale!=="ko"&&(sa||W!=="onCompositionStart"?W==="onCompositionEnd"&&sa&&(k=ps()):(cl=N,Ui="value"in cl?cl.value:cl.textContent,sa=!0)),it=Lu(p,W),0<it.length&&(W=new Ts(W,t,null,l,N),D.push({event:W,listeners:it}),k?W.data=k:(k=Ns(l),k!==null&&(W.data=k)))),(k=Wv?Fv(t,l):Iv(t,l))&&(W=Lu(p,"onBeforeInput"),0<W.length&&(it=new Ts("onBeforeInput","beforeinput",null,l,N),D.push({event:it,listeners:W}),it.data=k)),Vh(D,t,p,l,N)}rd(D,e)})}function On(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Lu(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Ka(t,l),n!=null&&a.unshift(On(t,n,u)),n=Ka(t,e),n!=null&&a.push(On(t,n,u))),t.tag===3)return a;t=t.return}return[]}function Ca(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function md(t,e,l,a,n){for(var u=e._reactName,i=[];l!==null&&l!==a;){var f=l,r=f.alternate,p=f.stateNode;if(f=f.tag,r!==null&&r===a)break;f!==5&&f!==26&&f!==27||p===null||(r=p,n?(p=Ka(l,u),p!=null&&i.unshift(On(l,p,r))):n||(p=Ka(l,u),p!=null&&i.push(On(l,p,r)))),l=l.return}i.length!==0&&t.push({event:e,listeners:i})}var Kh=/\r\n?/g,kh=/\u0000|\uFFFD/g;function vd(t){return(typeof t=="string"?t:""+t).replace(Kh,`
`).replace(kh,"")}function hd(t,e){return e=vd(e),vd(t)===e}function Zu(){}function Tt(t,e,l,a,n,u){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||ia(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&ia(t,""+a);break;case"className":Wn(t,"class",a);break;case"tabIndex":Wn(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Wn(t,l,a);break;case"style":ys(t,a,u);break;case"data":if(e!=="object"){Wn(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Pn(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&Tt(t,e,"name",n.name,n,null),Tt(t,e,"formEncType",n.formEncType,n,null),Tt(t,e,"formMethod",n.formMethod,n,null),Tt(t,e,"formTarget",n.formTarget,n,null)):(Tt(t,e,"encType",n.encType,n,null),Tt(t,e,"method",n.method,n,null),Tt(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Pn(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=Zu);break;case"onScroll":a!=null&&rt("scroll",t);break;case"onScrollEnd":a!=null&&rt("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=Pn(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":rt("beforetoggle",t),rt("toggle",t),$n(t,"popover",a);break;case"xlinkActuate":He(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":He(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":He(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":He(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":He(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":He(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":He(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":He(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":He(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":$n(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Av.get(l)||l,$n(t,l,a))}}function df(t,e,l,a,n,u){switch(l){case"style":ys(t,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"children":typeof a=="string"?ia(t,a):(typeof a=="number"||typeof a=="bigint")&&ia(t,""+a);break;case"onScroll":a!=null&&rt("scroll",t);break;case"onScrollEnd":a!=null&&rt("scrollend",t);break;case"onClick":a!=null&&(t.onclick=Zu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!us.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),u=t[Pt]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):$n(t,l,a)}}}function kt(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":rt("error",t),rt("load",t);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var i=l[u];if(i!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:Tt(t,e,u,i,l,null)}}n&&Tt(t,e,"srcSet",l.srcSet,l,null),a&&Tt(t,e,"src",l.src,l,null);return;case"input":rt("invalid",t);var f=u=i=n=null,r=null,p=null;for(a in l)if(l.hasOwnProperty(a)){var N=l[a];if(N!=null)switch(a){case"name":n=N;break;case"type":i=N;break;case"checked":r=N;break;case"defaultChecked":p=N;break;case"value":u=N;break;case"defaultValue":f=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(s(137,e));break;default:Tt(t,e,a,N,l,null)}}ds(t,u,f,r,p,i,n,!1),Fn(t);return;case"select":rt("invalid",t),a=i=u=null;for(n in l)if(l.hasOwnProperty(n)&&(f=l[n],f!=null))switch(n){case"value":u=f;break;case"defaultValue":i=f;break;case"multiple":a=f;default:Tt(t,e,n,f,l,null)}e=u,l=i,t.multiple=!!a,e!=null?ua(t,!!a,e,!1):l!=null&&ua(t,!!a,l,!0);return;case"textarea":rt("invalid",t),u=n=a=null;for(i in l)if(l.hasOwnProperty(i)&&(f=l[i],f!=null))switch(i){case"value":a=f;break;case"defaultValue":n=f;break;case"children":u=f;break;case"dangerouslySetInnerHTML":if(f!=null)throw Error(s(91));break;default:Tt(t,e,i,f,l,null)}vs(t,a,n,u),Fn(t);return;case"option":for(r in l)if(l.hasOwnProperty(r)&&(a=l[r],a!=null))switch(r){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Tt(t,e,r,a,l,null)}return;case"dialog":rt("beforetoggle",t),rt("toggle",t),rt("cancel",t),rt("close",t);break;case"iframe":case"object":rt("load",t);break;case"video":case"audio":for(a=0;a<Nn.length;a++)rt(Nn[a],t);break;case"image":rt("error",t),rt("load",t);break;case"details":rt("toggle",t);break;case"embed":case"source":case"link":rt("error",t),rt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(p in l)if(l.hasOwnProperty(p)&&(a=l[p],a!=null))switch(p){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:Tt(t,e,p,a,l,null)}return;default:if(zi(e)){for(N in l)l.hasOwnProperty(N)&&(a=l[N],a!==void 0&&df(t,e,N,a,l,void 0));return}}for(f in l)l.hasOwnProperty(f)&&(a=l[f],a!=null&&Tt(t,e,f,a,l,null))}function Jh(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,i=null,f=null,r=null,p=null,N=null;for(A in l){var D=l[A];if(l.hasOwnProperty(A)&&D!=null)switch(A){case"checked":break;case"value":break;case"defaultValue":r=D;default:a.hasOwnProperty(A)||Tt(t,e,A,null,a,D)}}for(var x in a){var A=a[x];if(D=l[x],a.hasOwnProperty(x)&&(A!=null||D!=null))switch(x){case"type":u=A;break;case"name":n=A;break;case"checked":p=A;break;case"defaultChecked":N=A;break;case"value":i=A;break;case"defaultValue":f=A;break;case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(s(137,e));break;default:A!==D&&Tt(t,e,x,A,a,D)}}_i(t,i,f,r,p,N,u,n);return;case"select":A=i=f=x=null;for(u in l)if(r=l[u],l.hasOwnProperty(u)&&r!=null)switch(u){case"value":break;case"multiple":A=r;default:a.hasOwnProperty(u)||Tt(t,e,u,null,a,r)}for(n in a)if(u=a[n],r=l[n],a.hasOwnProperty(n)&&(u!=null||r!=null))switch(n){case"value":x=u;break;case"defaultValue":f=u;break;case"multiple":i=u;default:u!==r&&Tt(t,e,n,u,a,r)}e=f,l=i,a=A,x!=null?ua(t,!!l,x,!1):!!a!=!!l&&(e!=null?ua(t,!!l,e,!0):ua(t,!!l,l?[]:"",!1));return;case"textarea":A=x=null;for(f in l)if(n=l[f],l.hasOwnProperty(f)&&n!=null&&!a.hasOwnProperty(f))switch(f){case"value":break;case"children":break;default:Tt(t,e,f,null,a,n)}for(i in a)if(n=a[i],u=l[i],a.hasOwnProperty(i)&&(n!=null||u!=null))switch(i){case"value":x=n;break;case"defaultValue":A=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(s(91));break;default:n!==u&&Tt(t,e,i,n,a,u)}ms(t,x,A);return;case"option":for(var I in l)if(x=l[I],l.hasOwnProperty(I)&&x!=null&&!a.hasOwnProperty(I))switch(I){case"selected":t.selected=!1;break;default:Tt(t,e,I,null,a,x)}for(r in a)if(x=a[r],A=l[r],a.hasOwnProperty(r)&&x!==A&&(x!=null||A!=null))switch(r){case"selected":t.selected=x&&typeof x!="function"&&typeof x!="symbol";break;default:Tt(t,e,r,x,a,A)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var J in l)x=l[J],l.hasOwnProperty(J)&&x!=null&&!a.hasOwnProperty(J)&&Tt(t,e,J,null,a,x);for(p in a)if(x=a[p],A=l[p],a.hasOwnProperty(p)&&x!==A&&(x!=null||A!=null))switch(p){case"children":case"dangerouslySetInnerHTML":if(x!=null)throw Error(s(137,e));break;default:Tt(t,e,p,x,a,A)}return;default:if(zi(e)){for(var At in l)x=l[At],l.hasOwnProperty(At)&&x!==void 0&&!a.hasOwnProperty(At)&&df(t,e,At,void 0,a,x);for(N in a)x=a[N],A=l[N],!a.hasOwnProperty(N)||x===A||x===void 0&&A===void 0||df(t,e,N,x,a,A);return}}for(var y in l)x=l[y],l.hasOwnProperty(y)&&x!=null&&!a.hasOwnProperty(y)&&Tt(t,e,y,null,a,x);for(D in a)x=a[D],A=l[D],!a.hasOwnProperty(D)||x===A||x==null&&A==null||Tt(t,e,D,x,a,A)}var mf=null,vf=null;function Ku(t){return t.nodeType===9?t:t.ownerDocument}function yd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function gd(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function hf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var yf=null;function $h(){var t=window.event;return t&&t.type==="popstate"?t===yf?!1:(yf=t,!0):(yf=null,!1)}var bd=typeof setTimeout=="function"?setTimeout:void 0,Wh=typeof clearTimeout=="function"?clearTimeout:void 0,pd=typeof Promise=="function"?Promise:void 0,Fh=typeof queueMicrotask=="function"?queueMicrotask:typeof pd<"u"?function(t){return pd.resolve(null).then(t).catch(Ih)}:bd;function Ih(t){setTimeout(function(){throw t})}function Al(t){return t==="head"}function Sd(t,e){var l=e,a=0,n=0;do{var u=l.nextSibling;if(t.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var i=t.ownerDocument;if(l&1&&Rn(i.documentElement),l&2&&Rn(i.body),l&4)for(l=i.head,Rn(l),i=l.firstChild;i;){var f=i.nextSibling,r=i.nodeName;i[La]||r==="SCRIPT"||r==="STYLE"||r==="LINK"&&i.rel.toLowerCase()==="stylesheet"||l.removeChild(i),i=f}}if(n===0){t.removeChild(u),Bn(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);Bn(e)}function gf(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":gf(l),xi(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function Ph(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[La])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Oe(t.nextSibling),t===null)break}return null}function ty(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Oe(t.nextSibling),t===null))return null;return t}function bf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function ey(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Oe(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var pf=null;function xd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function Td(t,e,l){switch(e=Ku(l),t){case"html":if(t=e.documentElement,!t)throw Error(s(452));return t;case"head":if(t=e.head,!t)throw Error(s(453));return t;case"body":if(t=e.body,!t)throw Error(s(454));return t;default:throw Error(s(451))}}function Rn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);xi(t)}var Me=new Map,Ad=new Set;function ku(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var We=B.d;B.d={f:ly,r:ay,D:ny,C:uy,L:iy,m:cy,X:sy,S:fy,M:oy};function ly(){var t=We.f(),e=Bu();return t||e}function ay(t){var e=ea(t);e!==null&&e.tag===5&&e.type==="form"?Qo(e):We.r(t)}var wa=typeof document>"u"?null:document;function Ed(t,e,l){var a=wa;if(a&&typeof e=="string"&&e){var n=pe(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),Ad.has(n)||(Ad.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),kt(e,"link",t),Xt(e),a.head.appendChild(e)))}}function ny(t){We.D(t),Ed("dns-prefetch",t,null)}function uy(t,e){We.C(t,e),Ed("preconnect",t,e)}function iy(t,e,l){We.L(t,e,l);var a=wa;if(a&&t&&e){var n='link[rel="preload"][as="'+pe(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+pe(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+pe(l.imageSizes)+'"]')):n+='[href="'+pe(t)+'"]';var u=n;switch(e){case"style":u=Ha(t);break;case"script":u=qa(t)}Me.has(u)||(t=z({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),Me.set(u,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(Dn(u))||e==="script"&&a.querySelector(Un(u))||(e=a.createElement("link"),kt(e,"link",t),Xt(e),a.head.appendChild(e)))}}function cy(t,e){We.m(t,e);var l=wa;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+pe(a)+'"][href="'+pe(t)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=qa(t)}if(!Me.has(u)&&(t=z({rel:"modulepreload",href:t},e),Me.set(u,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Un(u)))return}a=l.createElement("link"),kt(a,"link",t),Xt(a),l.head.appendChild(a)}}}function fy(t,e,l){We.S(t,e,l);var a=wa;if(a&&t){var n=la(a).hoistableStyles,u=Ha(t);e=e||"default";var i=n.get(u);if(!i){var f={loading:0,preload:null};if(i=a.querySelector(Dn(u)))f.loading=5;else{t=z({rel:"stylesheet",href:t,"data-precedence":e},l),(l=Me.get(u))&&Sf(t,l);var r=i=a.createElement("link");Xt(r),kt(r,"link",t),r._p=new Promise(function(p,N){r.onload=p,r.onerror=N}),r.addEventListener("load",function(){f.loading|=1}),r.addEventListener("error",function(){f.loading|=2}),f.loading|=4,Ju(i,e,a)}i={type:"stylesheet",instance:i,count:1,state:f},n.set(u,i)}}}function sy(t,e){We.X(t,e);var l=wa;if(l&&t){var a=la(l).hoistableScripts,n=qa(t),u=a.get(n);u||(u=l.querySelector(Un(n)),u||(t=z({src:t,async:!0},e),(e=Me.get(n))&&xf(t,e),u=l.createElement("script"),Xt(u),kt(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function oy(t,e){We.M(t,e);var l=wa;if(l&&t){var a=la(l).hoistableScripts,n=qa(t),u=a.get(n);u||(u=l.querySelector(Un(n)),u||(t=z({src:t,async:!0,type:"module"},e),(e=Me.get(n))&&xf(t,e),u=l.createElement("script"),Xt(u),kt(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function _d(t,e,l,a){var n=(n=P.current)?ku(n):null;if(!n)throw Error(s(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=Ha(l.href),l=la(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=Ha(l.href);var u=la(n).hoistableStyles,i=u.get(t);if(i||(n=n.ownerDocument||n,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,i),(u=n.querySelector(Dn(t)))&&!u._p&&(i.instance=u,i.state.loading=5),Me.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Me.set(t,l),u||ry(n,t,l,i.state))),e&&a===null)throw Error(s(528,""));return i}if(e&&a!==null)throw Error(s(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=qa(l),l=la(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,t))}}function Ha(t){return'href="'+pe(t)+'"'}function Dn(t){return'link[rel="stylesheet"]['+t+"]"}function Md(t){return z({},t,{"data-precedence":t.precedence,precedence:null})}function ry(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),kt(e,"link",l),Xt(e),t.head.appendChild(e))}function qa(t){return'[src="'+pe(t)+'"]'}function Un(t){return"script[async]"+t}function zd(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+pe(l.href)+'"]');if(a)return e.instance=a,Xt(a),a;var n=z({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Xt(a),kt(a,"style",n),Ju(a,l.precedence,t),e.instance=a;case"stylesheet":n=Ha(l.href);var u=t.querySelector(Dn(n));if(u)return e.state.loading|=4,e.instance=u,Xt(u),u;a=Md(l),(n=Me.get(n))&&Sf(a,n),u=(t.ownerDocument||t).createElement("link"),Xt(u);var i=u;return i._p=new Promise(function(f,r){i.onload=f,i.onerror=r}),kt(u,"link",a),e.state.loading|=4,Ju(u,l.precedence,t),e.instance=u;case"script":return u=qa(l.src),(n=t.querySelector(Un(u)))?(e.instance=n,Xt(n),n):(a=l,(n=Me.get(u))&&(a=z({},l),xf(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Xt(n),kt(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(s(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Ju(a,l.precedence,t));return e.instance}function Ju(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,i=0;i<a.length;i++){var f=a[i];if(f.dataset.precedence===e)u=f;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function Sf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function xf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var $u=null;function Nd(t,e,l){if($u===null){var a=new Map,n=$u=new Map;n.set(l,a)}else n=$u,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var u=l[n];if(!(u[La]||u[$t]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var i=u.getAttribute(e)||"";i=t+i;var f=a.get(i);f?f.push(u):a.set(i,[u])}}return a}function Od(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function dy(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Rd(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var jn=null;function my(){}function vy(t,e,l){if(jn===null)throw Error(s(475));var a=jn;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=Ha(l.href),u=t.querySelector(Dn(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Wu.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=u,Xt(u);return}u=t.ownerDocument||t,l=Md(l),(n=Me.get(n))&&Sf(l,n),u=u.createElement("link"),Xt(u);var i=u;i._p=new Promise(function(f,r){i.onload=f,i.onerror=r}),kt(u,"link",l),e.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Wu.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function hy(){if(jn===null)throw Error(s(475));var t=jn;return t.stylesheets&&t.count===0&&Tf(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&Tf(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Wu(){if(this.count--,this.count===0){if(this.stylesheets)Tf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Fu=null;function Tf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Fu=new Map,e.forEach(yy,t),Fu=null,Wu.call(t))}function yy(t,e){if(!(e.state.loading&4)){var l=Fu.get(t);if(l)var a=l.get(null);else{l=new Map,Fu.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var i=n[u];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(l.set(i.dataset.precedence,i),a=i)}a&&l.set(null,a)}n=e.instance,i=n.getAttribute("data-precedence"),u=l.get(i)||a,u===a&&l.set(null,n),l.set(i,n),this.count++,a=Wu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Cn={$$typeof:et,Provider:null,Consumer:null,_currentValue:C,_currentValue2:C,_threadCount:0};function gy(t,e,l,a,n,u,i,f){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=gi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gi(0),this.hiddenUpdates=gi(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=f,this.incompleteTransitions=new Map}function Dd(t,e,l,a,n,u,i,f,r,p,N,D){return t=new gy(t,e,l,i,f,r,p,D),e=1,u===!0&&(e|=24),u=se(3,null,null,e),t.current=u,u.stateNode=t,e=lc(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:e},ic(u),t}function Ud(t){return t?(t=va,t):va}function jd(t,e,l,a,n,u){n=Ud(n),a.context===null?a.context=n:a.pendingContext=n,a=ol(e),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=rl(t,a,e),l!==null&&(ve(l,t,e),on(l,t,e))}function Cd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function Af(t,e){Cd(t,e),(t=t.alternate)&&Cd(t,e)}function wd(t){if(t.tag===13){var e=ma(t,67108864);e!==null&&ve(e,t,67108864),Af(t,67108864)}}var Iu=!0;function by(t,e,l,a){var n=_.T;_.T=null;var u=B.p;try{B.p=2,Ef(t,e,l,a)}finally{B.p=u,_.T=n}}function py(t,e,l,a){var n=_.T;_.T=null;var u=B.p;try{B.p=8,Ef(t,e,l,a)}finally{B.p=u,_.T=n}}function Ef(t,e,l,a){if(Iu){var n=_f(a);if(n===null)rf(t,e,a,Pu,l),qd(t,a);else if(xy(n,t,e,l,a))a.stopPropagation();else if(qd(t,a),e&4&&-1<Sy.indexOf(t)){for(;n!==null;){var u=ea(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var i=Dl(u.pendingLanes);if(i!==0){var f=u;for(f.pendingLanes|=2,f.entangledLanes|=2;i;){var r=1<<31-ce(i);f.entanglements[1]|=r,i&=~r}we(u),(pt&6)===0&&(Hu=Re()+500,zn(0))}}break;case 13:f=ma(u,2),f!==null&&ve(f,u,2),Bu(),Af(u,2)}if(u=_f(a),u===null&&rf(t,e,a,Pu,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else rf(t,e,a,null,l)}}function _f(t){return t=Oi(t),Mf(t)}var Pu=null;function Mf(t){if(Pu=null,t=ta(t),t!==null){var e=T(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=M(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Pu=t,null}function Hd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(uv()){case $f:return 2;case Wf:return 8;case Zn:case iv:return 32;case Ff:return 268435456;default:return 32}default:return 32}}var zf=!1,El=null,_l=null,Ml=null,wn=new Map,Hn=new Map,zl=[],Sy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function qd(t,e){switch(t){case"focusin":case"focusout":El=null;break;case"dragenter":case"dragleave":_l=null;break;case"mouseover":case"mouseout":Ml=null;break;case"pointerover":case"pointerout":wn.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Hn.delete(e.pointerId)}}function qn(t,e,l,a,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},e!==null&&(e=ea(e),e!==null&&wd(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function xy(t,e,l,a,n){switch(e){case"focusin":return El=qn(El,t,e,l,a,n),!0;case"dragenter":return _l=qn(_l,t,e,l,a,n),!0;case"mouseover":return Ml=qn(Ml,t,e,l,a,n),!0;case"pointerover":var u=n.pointerId;return wn.set(u,qn(wn.get(u)||null,t,e,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Hn.set(u,qn(Hn.get(u)||null,t,e,l,a,n)),!0}return!1}function Bd(t){var e=ta(t.target);if(e!==null){var l=T(e);if(l!==null){if(e=l.tag,e===13){if(e=M(l),e!==null){t.blockedOn=e,vv(t.priority,function(){if(l.tag===13){var a=me();a=bi(a);var n=ma(l,a);n!==null&&ve(n,l,a),Af(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function ti(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=_f(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);Ni=a,l.target.dispatchEvent(a),Ni=null}else return e=ea(l),e!==null&&wd(e),t.blockedOn=l,!1;e.shift()}return!0}function Gd(t,e,l){ti(t)&&l.delete(e)}function Ty(){zf=!1,El!==null&&ti(El)&&(El=null),_l!==null&&ti(_l)&&(_l=null),Ml!==null&&ti(Ml)&&(Ml=null),wn.forEach(Gd),Hn.forEach(Gd)}function ei(t,e){t.blockedOn===e&&(t.blockedOn=null,zf||(zf=!0,c.unstable_scheduleCallback(c.unstable_NormalPriority,Ty)))}var li=null;function Yd(t){li!==t&&(li=t,c.unstable_scheduleCallback(c.unstable_NormalPriority,function(){li===t&&(li=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(Mf(a||l)===null)continue;break}var u=ea(l);u!==null&&(t.splice(e,3),e-=3,_c(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function Bn(t){function e(r){return ei(r,t)}El!==null&&ei(El,t),_l!==null&&ei(_l,t),Ml!==null&&ei(Ml,t),wn.forEach(e),Hn.forEach(e);for(var l=0;l<zl.length;l++){var a=zl[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<zl.length&&(l=zl[0],l.blockedOn===null);)Bd(l),l.blockedOn===null&&zl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],i=n[Pt]||null;if(typeof u=="function")i||Yd(l);else if(i){var f=null;if(u&&u.hasAttribute("formAction")){if(n=u,i=u[Pt]||null)f=i.formAction;else if(Mf(n)!==null)continue}else f=i.action;typeof f=="function"?l[a+1]=f:(l.splice(a,3),a-=3),Yd(l)}}}function Nf(t){this._internalRoot=t}ai.prototype.render=Nf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(s(409));var l=e.current,a=me();jd(l,a,t,e,null,null)},ai.prototype.unmount=Nf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;jd(t.current,2,null,t,null,null),Bu(),e[Pl]=null}};function ai(t){this._internalRoot=t}ai.prototype.unstable_scheduleHydration=function(t){if(t){var e=ls();t={blockedOn:null,target:t,priority:e};for(var l=0;l<zl.length&&e!==0&&e<zl[l].priority;l++);zl.splice(l,0,t),l===0&&Bd(t)}};var Xd=o.version;if(Xd!=="19.1.0")throw Error(s(527,Xd,"19.1.0"));B.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(s(188)):(t=Object.keys(t).join(","),Error(s(268,t)));return t=E(e),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var Ay={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:_,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ni=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ni.isDisabled&&ni.supportsFiber)try{Xa=ni.inject(Ay),ie=ni}catch{}}return Yn.createRoot=function(t,e){if(!h(t))throw Error(s(299));var l=!1,a="",n=ar,u=nr,i=ur,f=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(i=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(f=e.unstable_transitionCallbacks)),e=Dd(t,1,!1,null,null,l,a,n,u,i,f,null),t[Pl]=e.current,of(t),new Nf(e)},Yn.hydrateRoot=function(t,e,l){if(!h(t))throw Error(s(299));var a=!1,n="",u=ar,i=nr,f=ur,r=null,p=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(i=l.onCaughtError),l.onRecoverableError!==void 0&&(f=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(r=l.unstable_transitionCallbacks),l.formState!==void 0&&(p=l.formState)),e=Dd(t,1,!0,e,l??null,a,n,u,i,f,r,p),e.context=Ud(null),l=e.current,a=me(),a=bi(a),n=ol(a),n.callback=null,rl(l,n,a),l=a,e.current.lanes=l,Qa(e,l),we(e),t[Pl]=e.current,of(t),new ai(e)},Yn.version="19.1.0",Yn}var Fd;function Cy(){if(Fd)return Df.exports;Fd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(o){console.error(o)}}return c(),Df.exports=jy(),Df.exports}var wy=Cy();function Id(c,o){if(typeof c=="function")return c(o);c!=null&&(c.current=o)}function Hy(...c){return o=>{let d=!1;const s=c.map(h=>{const T=Id(h,o);return!d&&typeof T=="function"&&(d=!0),T});if(d)return()=>{for(let h=0;h<s.length;h++){const T=s[h];typeof T=="function"?T():Id(c[h],null)}}}}function Qn(...c){return q.useCallback(Hy(...c),c)}function oi(c){const o=qy(c),d=q.forwardRef((s,h)=>{const{children:T,...M}=s,R=q.Children.toArray(T),E=R.find(Gy);if(E){const g=E.props.children,z=R.map(H=>H===E?q.Children.count(g)>1?q.Children.only(null):q.isValidElement(g)?g.props.children:null:H);return S.jsx(o,{...M,ref:h,children:q.isValidElement(g)?q.cloneElement(g,void 0,z):null})}return S.jsx(o,{...M,ref:h,children:T})});return d.displayName=`${c}.Slot`,d}var gm=oi("Slot");function qy(c){const o=q.forwardRef((d,s)=>{const{children:h,...T}=d,M=q.isValidElement(h)?Xy(h):void 0,R=Qn(M,s);if(q.isValidElement(h)){const E=Yy(T,h.props);return h.type!==q.Fragment&&(E.ref=R),q.cloneElement(h,E)}return q.Children.count(h)>1?q.Children.only(null):null});return o.displayName=`${c}.SlotClone`,o}var By=Symbol("radix.slottable");function Gy(c){return q.isValidElement(c)&&typeof c.type=="function"&&"__radixId"in c.type&&c.type.__radixId===By}function Yy(c,o){const d={...o};for(const s in o){const h=c[s],T=o[s];/^on[A-Z]/.test(s)?h&&T?d[s]=(...R)=>{const E=T(...R);return h(...R),E}:h&&(d[s]=h):s==="style"?d[s]={...h,...T}:s==="className"&&(d[s]=[h,T].filter(Boolean).join(" "))}return{...c,...d}}function Xy(c){var s,h;let o=(s=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:s.get,d=o&&"isReactWarning"in o&&o.isReactWarning;return d?c.ref:(o=(h=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:h.get,d=o&&"isReactWarning"in o&&o.isReactWarning,d?c.props.ref:c.props.ref||c.ref)}function bm(c){var o,d,s="";if(typeof c=="string"||typeof c=="number")s+=c;else if(typeof c=="object")if(Array.isArray(c)){var h=c.length;for(o=0;o<h;o++)c[o]&&(d=bm(c[o]))&&(s&&(s+=" "),s+=d)}else for(d in c)c[d]&&(s&&(s+=" "),s+=d);return s}function pm(){for(var c,o,d=0,s="",h=arguments.length;d<h;d++)(c=arguments[d])&&(o=bm(c))&&(s&&(s+=" "),s+=o);return s}const Pd=c=>typeof c=="boolean"?`${c}`:c===0?"0":c,tm=pm,Sm=(c,o)=>d=>{var s;if((o==null?void 0:o.variants)==null)return tm(c,d==null?void 0:d.class,d==null?void 0:d.className);const{variants:h,defaultVariants:T}=o,M=Object.keys(h).map(g=>{const z=d==null?void 0:d[g],H=T==null?void 0:T[g];if(z===null)return null;const Y=Pd(z)||Pd(H);return h[g][Y]}),R=d&&Object.entries(d).reduce((g,z)=>{let[H,Y]=z;return Y===void 0||(g[H]=Y),g},{}),E=o==null||(s=o.compoundVariants)===null||s===void 0?void 0:s.reduce((g,z)=>{let{class:H,className:Y,...tt}=z;return Object.entries(tt).every(F=>{let[$,K]=F;return Array.isArray(K)?K.includes({...T,...R}[$]):{...T,...R}[$]===K})?[...g,H,Y]:g},[]);return tm(c,M,E,d==null?void 0:d.class,d==null?void 0:d.className)},Zf="-",Vy=c=>{const o=Ly(c),{conflictingClassGroups:d,conflictingClassGroupModifiers:s}=c;return{getClassGroupId:M=>{const R=M.split(Zf);return R[0]===""&&R.length!==1&&R.shift(),xm(R,o)||Qy(M)},getConflictingClassGroupIds:(M,R)=>{const E=d[M]||[];return R&&s[M]?[...E,...s[M]]:E}}},xm=(c,o)=>{var M;if(c.length===0)return o.classGroupId;const d=c[0],s=o.nextPart.get(d),h=s?xm(c.slice(1),s):void 0;if(h)return h;if(o.validators.length===0)return;const T=c.join(Zf);return(M=o.validators.find(({validator:R})=>R(T)))==null?void 0:M.classGroupId},em=/^\[(.+)\]$/,Qy=c=>{if(em.test(c)){const o=em.exec(c)[1],d=o==null?void 0:o.substring(0,o.indexOf(":"));if(d)return"arbitrary.."+d}},Ly=c=>{const{theme:o,classGroups:d}=c,s={nextPart:new Map,validators:[]};for(const h in d)Yf(d[h],s,h,o);return s},Yf=(c,o,d,s)=>{c.forEach(h=>{if(typeof h=="string"){const T=h===""?o:lm(o,h);T.classGroupId=d;return}if(typeof h=="function"){if(Zy(h)){Yf(h(s),o,d,s);return}o.validators.push({validator:h,classGroupId:d});return}Object.entries(h).forEach(([T,M])=>{Yf(M,lm(o,T),d,s)})})},lm=(c,o)=>{let d=c;return o.split(Zf).forEach(s=>{d.nextPart.has(s)||d.nextPart.set(s,{nextPart:new Map,validators:[]}),d=d.nextPart.get(s)}),d},Zy=c=>c.isThemeGetter,Ky=c=>{if(c<1)return{get:()=>{},set:()=>{}};let o=0,d=new Map,s=new Map;const h=(T,M)=>{d.set(T,M),o++,o>c&&(o=0,s=d,d=new Map)};return{get(T){let M=d.get(T);if(M!==void 0)return M;if((M=s.get(T))!==void 0)return h(T,M),M},set(T,M){d.has(T)?d.set(T,M):h(T,M)}}},Xf="!",Vf=":",ky=Vf.length,Jy=c=>{const{prefix:o,experimentalParseClassName:d}=c;let s=h=>{const T=[];let M=0,R=0,E=0,g;for(let F=0;F<h.length;F++){let $=h[F];if(M===0&&R===0){if($===Vf){T.push(h.slice(E,F)),E=F+ky;continue}if($==="/"){g=F;continue}}$==="["?M++:$==="]"?M--:$==="("?R++:$===")"&&R--}const z=T.length===0?h:h.substring(E),H=$y(z),Y=H!==z,tt=g&&g>E?g-E:void 0;return{modifiers:T,hasImportantModifier:Y,baseClassName:H,maybePostfixModifierPosition:tt}};if(o){const h=o+Vf,T=s;s=M=>M.startsWith(h)?T(M.substring(h.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:M,maybePostfixModifierPosition:void 0}}if(d){const h=s;s=T=>d({className:T,parseClassName:h})}return s},$y=c=>c.endsWith(Xf)?c.substring(0,c.length-1):c.startsWith(Xf)?c.substring(1):c,Wy=c=>{const o=Object.fromEntries(c.orderSensitiveModifiers.map(s=>[s,!0]));return s=>{if(s.length<=1)return s;const h=[];let T=[];return s.forEach(M=>{M[0]==="["||o[M]?(h.push(...T.sort(),M),T=[]):T.push(M)}),h.push(...T.sort()),h}},Fy=c=>({cache:Ky(c.cacheSize),parseClassName:Jy(c),sortModifiers:Wy(c),...Vy(c)}),Iy=/\s+/,Py=(c,o)=>{const{parseClassName:d,getClassGroupId:s,getConflictingClassGroupIds:h,sortModifiers:T}=o,M=[],R=c.trim().split(Iy);let E="";for(let g=R.length-1;g>=0;g-=1){const z=R[g],{isExternal:H,modifiers:Y,hasImportantModifier:tt,baseClassName:F,maybePostfixModifierPosition:$}=d(z);if(H){E=z+(E.length>0?" "+E:E);continue}let K=!!$,ct=s(K?F.substring(0,$):F);if(!ct){if(!K){E=z+(E.length>0?" "+E:E);continue}if(ct=s(F),!ct){E=z+(E.length>0?" "+E:E);continue}K=!1}const gt=T(Y).join(":"),et=tt?gt+Xf:gt,ft=et+ct;if(M.includes(ft))continue;M.push(ft);const j=h(ct,K);for(let lt=0;lt<j.length;++lt){const bt=j[lt];M.push(et+bt)}E=z+(E.length>0?" "+E:E)}return E};function tg(){let c=0,o,d,s="";for(;c<arguments.length;)(o=arguments[c++])&&(d=Tm(o))&&(s&&(s+=" "),s+=d);return s}const Tm=c=>{if(typeof c=="string")return c;let o,d="";for(let s=0;s<c.length;s++)c[s]&&(o=Tm(c[s]))&&(d&&(d+=" "),d+=o);return d};function eg(c,...o){let d,s,h,T=M;function M(E){const g=o.reduce((z,H)=>H(z),c());return d=Fy(g),s=d.cache.get,h=d.cache.set,T=R,R(E)}function R(E){const g=s(E);if(g)return g;const z=Py(E,d);return h(E,z),z}return function(){return T(tg.apply(null,arguments))}}const Yt=c=>{const o=d=>d[c]||[];return o.isThemeGetter=!0,o},Am=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Em=/^\((?:(\w[\w-]*):)?(.+)\)$/i,lg=/^\d+\/\d+$/,ag=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ng=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ug=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ig=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,cg=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ba=c=>lg.test(c),ut=c=>!!c&&!Number.isNaN(Number(c)),Ol=c=>!!c&&Number.isInteger(Number(c)),wf=c=>c.endsWith("%")&&ut(c.slice(0,-1)),Fe=c=>ag.test(c),fg=()=>!0,sg=c=>ng.test(c)&&!ug.test(c),_m=()=>!1,og=c=>ig.test(c),rg=c=>cg.test(c),dg=c=>!L(c)&&!Z(c),mg=c=>Ga(c,Nm,_m),L=c=>Am.test(c),$l=c=>Ga(c,Om,sg),Hf=c=>Ga(c,bg,ut),am=c=>Ga(c,Mm,_m),vg=c=>Ga(c,zm,rg),ui=c=>Ga(c,Rm,og),Z=c=>Em.test(c),Xn=c=>Ya(c,Om),hg=c=>Ya(c,pg),nm=c=>Ya(c,Mm),yg=c=>Ya(c,Nm),gg=c=>Ya(c,zm),ii=c=>Ya(c,Rm,!0),Ga=(c,o,d)=>{const s=Am.exec(c);return s?s[1]?o(s[1]):d(s[2]):!1},Ya=(c,o,d=!1)=>{const s=Em.exec(c);return s?s[1]?o(s[1]):d:!1},Mm=c=>c==="position"||c==="percentage",zm=c=>c==="image"||c==="url",Nm=c=>c==="length"||c==="size"||c==="bg-size",Om=c=>c==="length",bg=c=>c==="number",pg=c=>c==="family-name",Rm=c=>c==="shadow",Sg=()=>{const c=Yt("color"),o=Yt("font"),d=Yt("text"),s=Yt("font-weight"),h=Yt("tracking"),T=Yt("leading"),M=Yt("breakpoint"),R=Yt("container"),E=Yt("spacing"),g=Yt("radius"),z=Yt("shadow"),H=Yt("inset-shadow"),Y=Yt("text-shadow"),tt=Yt("drop-shadow"),F=Yt("blur"),$=Yt("perspective"),K=Yt("aspect"),ct=Yt("ease"),gt=Yt("animate"),et=()=>["auto","avoid","all","avoid-page","page","left","right","column"],ft=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],j=()=>[...ft(),Z,L],lt=()=>["auto","hidden","clip","visible","scroll"],bt=()=>["auto","contain","none"],G=()=>[Z,L,E],Nt=()=>[Ba,"full","auto",...G()],he=()=>[Ol,"none","subgrid",Z,L],Jt=()=>["auto",{span:["full",Ol,Z,L]},Ol,Z,L],Rt=()=>[Ol,"auto",Z,L],ye=()=>["auto","min","max","fr",Z,L],ge=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],zt=()=>["start","end","center","stretch","center-safe","end-safe"],_=()=>["auto",...G()],B=()=>[Ba,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...G()],C=()=>[c,Z,L],yt=()=>[...ft(),nm,am,{position:[Z,L]}],m=()=>["no-repeat",{repeat:["","x","y","space","round"]}],U=()=>["auto","cover","contain",yg,mg,{size:[Z,L]}],X=()=>[wf,Xn,$l],w=()=>["","none","full",g,Z,L],V=()=>["",ut,Xn,$l],st=()=>["solid","dashed","dotted","double"],P=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],mt=()=>[ut,wf,nm,am],Et=()=>["","none",F,Z,L],ue=()=>["none",ut,Z,L],ll=()=>["none",ut,Z,L],al=()=>[ut,Z,L],nl=()=>[Ba,"full",...G()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Fe],breakpoint:[Fe],color:[fg],container:[Fe],"drop-shadow":[Fe],ease:["in","out","in-out"],font:[dg],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Fe],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Fe],shadow:[Fe],spacing:["px",ut],text:[Fe],"text-shadow":[Fe],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ba,L,Z,K]}],container:["container"],columns:[{columns:[ut,L,Z,R]}],"break-after":[{"break-after":et()}],"break-before":[{"break-before":et()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:j()}],overflow:[{overflow:lt()}],"overflow-x":[{"overflow-x":lt()}],"overflow-y":[{"overflow-y":lt()}],overscroll:[{overscroll:bt()}],"overscroll-x":[{"overscroll-x":bt()}],"overscroll-y":[{"overscroll-y":bt()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Nt()}],"inset-x":[{"inset-x":Nt()}],"inset-y":[{"inset-y":Nt()}],start:[{start:Nt()}],end:[{end:Nt()}],top:[{top:Nt()}],right:[{right:Nt()}],bottom:[{bottom:Nt()}],left:[{left:Nt()}],visibility:["visible","invisible","collapse"],z:[{z:[Ol,"auto",Z,L]}],basis:[{basis:[Ba,"full","auto",R,...G()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ut,Ba,"auto","initial","none",L]}],grow:[{grow:["",ut,Z,L]}],shrink:[{shrink:["",ut,Z,L]}],order:[{order:[Ol,"first","last","none",Z,L]}],"grid-cols":[{"grid-cols":he()}],"col-start-end":[{col:Jt()}],"col-start":[{"col-start":Rt()}],"col-end":[{"col-end":Rt()}],"grid-rows":[{"grid-rows":he()}],"row-start-end":[{row:Jt()}],"row-start":[{"row-start":Rt()}],"row-end":[{"row-end":Rt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ye()}],"auto-rows":[{"auto-rows":ye()}],gap:[{gap:G()}],"gap-x":[{"gap-x":G()}],"gap-y":[{"gap-y":G()}],"justify-content":[{justify:[...ge(),"normal"]}],"justify-items":[{"justify-items":[...zt(),"normal"]}],"justify-self":[{"justify-self":["auto",...zt()]}],"align-content":[{content:["normal",...ge()]}],"align-items":[{items:[...zt(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...zt(),{baseline:["","last"]}]}],"place-content":[{"place-content":ge()}],"place-items":[{"place-items":[...zt(),"baseline"]}],"place-self":[{"place-self":["auto",...zt()]}],p:[{p:G()}],px:[{px:G()}],py:[{py:G()}],ps:[{ps:G()}],pe:[{pe:G()}],pt:[{pt:G()}],pr:[{pr:G()}],pb:[{pb:G()}],pl:[{pl:G()}],m:[{m:_()}],mx:[{mx:_()}],my:[{my:_()}],ms:[{ms:_()}],me:[{me:_()}],mt:[{mt:_()}],mr:[{mr:_()}],mb:[{mb:_()}],ml:[{ml:_()}],"space-x":[{"space-x":G()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":G()}],"space-y-reverse":["space-y-reverse"],size:[{size:B()}],w:[{w:[R,"screen",...B()]}],"min-w":[{"min-w":[R,"screen","none",...B()]}],"max-w":[{"max-w":[R,"screen","none","prose",{screen:[M]},...B()]}],h:[{h:["screen","lh",...B()]}],"min-h":[{"min-h":["screen","lh","none",...B()]}],"max-h":[{"max-h":["screen","lh",...B()]}],"font-size":[{text:["base",d,Xn,$l]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,Z,Hf]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",wf,L]}],"font-family":[{font:[hg,L,o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[h,Z,L]}],"line-clamp":[{"line-clamp":[ut,"none",Z,Hf]}],leading:[{leading:[T,...G()]}],"list-image":[{"list-image":["none",Z,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Z,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:C()}],"text-color":[{text:C()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...st(),"wavy"]}],"text-decoration-thickness":[{decoration:[ut,"from-font","auto",Z,$l]}],"text-decoration-color":[{decoration:C()}],"underline-offset":[{"underline-offset":[ut,"auto",Z,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:G()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Z,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Z,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:yt()}],"bg-repeat":[{bg:m()}],"bg-size":[{bg:U()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Ol,Z,L],radial:["",Z,L],conic:[Ol,Z,L]},gg,vg]}],"bg-color":[{bg:C()}],"gradient-from-pos":[{from:X()}],"gradient-via-pos":[{via:X()}],"gradient-to-pos":[{to:X()}],"gradient-from":[{from:C()}],"gradient-via":[{via:C()}],"gradient-to":[{to:C()}],rounded:[{rounded:w()}],"rounded-s":[{"rounded-s":w()}],"rounded-e":[{"rounded-e":w()}],"rounded-t":[{"rounded-t":w()}],"rounded-r":[{"rounded-r":w()}],"rounded-b":[{"rounded-b":w()}],"rounded-l":[{"rounded-l":w()}],"rounded-ss":[{"rounded-ss":w()}],"rounded-se":[{"rounded-se":w()}],"rounded-ee":[{"rounded-ee":w()}],"rounded-es":[{"rounded-es":w()}],"rounded-tl":[{"rounded-tl":w()}],"rounded-tr":[{"rounded-tr":w()}],"rounded-br":[{"rounded-br":w()}],"rounded-bl":[{"rounded-bl":w()}],"border-w":[{border:V()}],"border-w-x":[{"border-x":V()}],"border-w-y":[{"border-y":V()}],"border-w-s":[{"border-s":V()}],"border-w-e":[{"border-e":V()}],"border-w-t":[{"border-t":V()}],"border-w-r":[{"border-r":V()}],"border-w-b":[{"border-b":V()}],"border-w-l":[{"border-l":V()}],"divide-x":[{"divide-x":V()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":V()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...st(),"hidden","none"]}],"divide-style":[{divide:[...st(),"hidden","none"]}],"border-color":[{border:C()}],"border-color-x":[{"border-x":C()}],"border-color-y":[{"border-y":C()}],"border-color-s":[{"border-s":C()}],"border-color-e":[{"border-e":C()}],"border-color-t":[{"border-t":C()}],"border-color-r":[{"border-r":C()}],"border-color-b":[{"border-b":C()}],"border-color-l":[{"border-l":C()}],"divide-color":[{divide:C()}],"outline-style":[{outline:[...st(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ut,Z,L]}],"outline-w":[{outline:["",ut,Xn,$l]}],"outline-color":[{outline:C()}],shadow:[{shadow:["","none",z,ii,ui]}],"shadow-color":[{shadow:C()}],"inset-shadow":[{"inset-shadow":["none",H,ii,ui]}],"inset-shadow-color":[{"inset-shadow":C()}],"ring-w":[{ring:V()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:C()}],"ring-offset-w":[{"ring-offset":[ut,$l]}],"ring-offset-color":[{"ring-offset":C()}],"inset-ring-w":[{"inset-ring":V()}],"inset-ring-color":[{"inset-ring":C()}],"text-shadow":[{"text-shadow":["none",Y,ii,ui]}],"text-shadow-color":[{"text-shadow":C()}],opacity:[{opacity:[ut,Z,L]}],"mix-blend":[{"mix-blend":[...P(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":P()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ut]}],"mask-image-linear-from-pos":[{"mask-linear-from":mt()}],"mask-image-linear-to-pos":[{"mask-linear-to":mt()}],"mask-image-linear-from-color":[{"mask-linear-from":C()}],"mask-image-linear-to-color":[{"mask-linear-to":C()}],"mask-image-t-from-pos":[{"mask-t-from":mt()}],"mask-image-t-to-pos":[{"mask-t-to":mt()}],"mask-image-t-from-color":[{"mask-t-from":C()}],"mask-image-t-to-color":[{"mask-t-to":C()}],"mask-image-r-from-pos":[{"mask-r-from":mt()}],"mask-image-r-to-pos":[{"mask-r-to":mt()}],"mask-image-r-from-color":[{"mask-r-from":C()}],"mask-image-r-to-color":[{"mask-r-to":C()}],"mask-image-b-from-pos":[{"mask-b-from":mt()}],"mask-image-b-to-pos":[{"mask-b-to":mt()}],"mask-image-b-from-color":[{"mask-b-from":C()}],"mask-image-b-to-color":[{"mask-b-to":C()}],"mask-image-l-from-pos":[{"mask-l-from":mt()}],"mask-image-l-to-pos":[{"mask-l-to":mt()}],"mask-image-l-from-color":[{"mask-l-from":C()}],"mask-image-l-to-color":[{"mask-l-to":C()}],"mask-image-x-from-pos":[{"mask-x-from":mt()}],"mask-image-x-to-pos":[{"mask-x-to":mt()}],"mask-image-x-from-color":[{"mask-x-from":C()}],"mask-image-x-to-color":[{"mask-x-to":C()}],"mask-image-y-from-pos":[{"mask-y-from":mt()}],"mask-image-y-to-pos":[{"mask-y-to":mt()}],"mask-image-y-from-color":[{"mask-y-from":C()}],"mask-image-y-to-color":[{"mask-y-to":C()}],"mask-image-radial":[{"mask-radial":[Z,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":mt()}],"mask-image-radial-to-pos":[{"mask-radial-to":mt()}],"mask-image-radial-from-color":[{"mask-radial-from":C()}],"mask-image-radial-to-color":[{"mask-radial-to":C()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":ft()}],"mask-image-conic-pos":[{"mask-conic":[ut]}],"mask-image-conic-from-pos":[{"mask-conic-from":mt()}],"mask-image-conic-to-pos":[{"mask-conic-to":mt()}],"mask-image-conic-from-color":[{"mask-conic-from":C()}],"mask-image-conic-to-color":[{"mask-conic-to":C()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:yt()}],"mask-repeat":[{mask:m()}],"mask-size":[{mask:U()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Z,L]}],filter:[{filter:["","none",Z,L]}],blur:[{blur:Et()}],brightness:[{brightness:[ut,Z,L]}],contrast:[{contrast:[ut,Z,L]}],"drop-shadow":[{"drop-shadow":["","none",tt,ii,ui]}],"drop-shadow-color":[{"drop-shadow":C()}],grayscale:[{grayscale:["",ut,Z,L]}],"hue-rotate":[{"hue-rotate":[ut,Z,L]}],invert:[{invert:["",ut,Z,L]}],saturate:[{saturate:[ut,Z,L]}],sepia:[{sepia:["",ut,Z,L]}],"backdrop-filter":[{"backdrop-filter":["","none",Z,L]}],"backdrop-blur":[{"backdrop-blur":Et()}],"backdrop-brightness":[{"backdrop-brightness":[ut,Z,L]}],"backdrop-contrast":[{"backdrop-contrast":[ut,Z,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ut,Z,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ut,Z,L]}],"backdrop-invert":[{"backdrop-invert":["",ut,Z,L]}],"backdrop-opacity":[{"backdrop-opacity":[ut,Z,L]}],"backdrop-saturate":[{"backdrop-saturate":[ut,Z,L]}],"backdrop-sepia":[{"backdrop-sepia":["",ut,Z,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":G()}],"border-spacing-x":[{"border-spacing-x":G()}],"border-spacing-y":[{"border-spacing-y":G()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Z,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ut,"initial",Z,L]}],ease:[{ease:["linear","initial",ct,Z,L]}],delay:[{delay:[ut,Z,L]}],animate:[{animate:["none",gt,Z,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[$,Z,L]}],"perspective-origin":[{"perspective-origin":j()}],rotate:[{rotate:ue()}],"rotate-x":[{"rotate-x":ue()}],"rotate-y":[{"rotate-y":ue()}],"rotate-z":[{"rotate-z":ue()}],scale:[{scale:ll()}],"scale-x":[{"scale-x":ll()}],"scale-y":[{"scale-y":ll()}],"scale-z":[{"scale-z":ll()}],"scale-3d":["scale-3d"],skew:[{skew:al()}],"skew-x":[{"skew-x":al()}],"skew-y":[{"skew-y":al()}],transform:[{transform:[Z,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:j()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:nl()}],"translate-x":[{"translate-x":nl()}],"translate-y":[{"translate-y":nl()}],"translate-z":[{"translate-z":nl()}],"translate-none":["translate-none"],accent:[{accent:C()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:C()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Z,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":G()}],"scroll-mx":[{"scroll-mx":G()}],"scroll-my":[{"scroll-my":G()}],"scroll-ms":[{"scroll-ms":G()}],"scroll-me":[{"scroll-me":G()}],"scroll-mt":[{"scroll-mt":G()}],"scroll-mr":[{"scroll-mr":G()}],"scroll-mb":[{"scroll-mb":G()}],"scroll-ml":[{"scroll-ml":G()}],"scroll-p":[{"scroll-p":G()}],"scroll-px":[{"scroll-px":G()}],"scroll-py":[{"scroll-py":G()}],"scroll-ps":[{"scroll-ps":G()}],"scroll-pe":[{"scroll-pe":G()}],"scroll-pt":[{"scroll-pt":G()}],"scroll-pr":[{"scroll-pr":G()}],"scroll-pb":[{"scroll-pb":G()}],"scroll-pl":[{"scroll-pl":G()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Z,L]}],fill:[{fill:["none",...C()]}],"stroke-w":[{stroke:[ut,Xn,$l,Hf]}],stroke:[{stroke:["none",...C()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},xg=eg(Sg);function ne(...c){return xg(pm(c))}const Tg=Sm("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function um({className:c,variant:o,size:d,asChild:s=!1,...h}){const T=s?gm:"button";return S.jsx(T,{"data-slot":"button",className:ne(Tg({variant:o,size:d,className:c})),...h})}function Ie({className:c,...o}){return S.jsx("div",{"data-slot":"card",className:ne("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",c),...o})}function Wl({className:c,...o}){return S.jsx("div",{"data-slot":"card-header",className:ne("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",c),...o})}function Fl({className:c,...o}){return S.jsx("div",{"data-slot":"card-title",className:ne("leading-none font-semibold",c),...o})}function qf({className:c,...o}){return S.jsx("div",{"data-slot":"card-description",className:ne("text-muted-foreground text-sm",c),...o})}function Pe({className:c,...o}){return S.jsx("div",{"data-slot":"card-content",className:ne("px-6",c),...o})}function im({className:c,type:o,...d}){return S.jsx("input",{type:o,"data-slot":"input",className:ne("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",c),...d})}ym();var Ag=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],el=Ag.reduce((c,o)=>{const d=oi(`Primitive.${o}`),s=q.forwardRef((h,T)=>{const{asChild:M,...R}=h,E=M?d:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),S.jsx(E,{...R,ref:T})});return s.displayName=`Primitive.${o}`,{...c,[o]:s}},{}),Eg="Label",Dm=q.forwardRef((c,o)=>S.jsx(el.label,{...c,ref:o,onMouseDown:d=>{var h;d.target.closest("button, input, select, textarea")||((h=c.onMouseDown)==null||h.call(c,d),!d.defaultPrevented&&d.detail>1&&d.preventDefault())}}));Dm.displayName=Eg;var _g=Dm;function cm({className:c,...o}){return S.jsx(_g,{"data-slot":"label",className:ne("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",c),...o})}function tl(c,o,{checkForDefaultPrevented:d=!0}={}){return function(h){if(c==null||c(h),d===!1||!h.defaultPrevented)return o==null?void 0:o(h)}}function mi(c,o=[]){let d=[];function s(T,M){const R=q.createContext(M),E=d.length;d=[...d,M];const g=H=>{var ct;const{scope:Y,children:tt,...F}=H,$=((ct=Y==null?void 0:Y[c])==null?void 0:ct[E])||R,K=q.useMemo(()=>F,Object.values(F));return S.jsx($.Provider,{value:K,children:tt})};g.displayName=T+"Provider";function z(H,Y){var $;const tt=(($=Y==null?void 0:Y[c])==null?void 0:$[E])||R,F=q.useContext(tt);if(F)return F;if(M!==void 0)return M;throw new Error(`\`${H}\` must be used within \`${T}\``)}return[g,z]}const h=()=>{const T=d.map(M=>q.createContext(M));return function(R){const E=(R==null?void 0:R[c])||T;return q.useMemo(()=>({[`__scope${c}`]:{...R,[c]:E}}),[R,E])}};return h.scopeName=c,[s,Mg(h,...o)]}function Mg(...c){const o=c[0];if(c.length===1)return o;const d=()=>{const s=c.map(h=>({useScope:h(),scopeName:h.scopeName}));return function(T){const M=s.reduce((R,{useScope:E,scopeName:g})=>{const H=E(T)[`__scope${g}`];return{...R,...H}},{});return q.useMemo(()=>({[`__scope${o.scopeName}`]:M}),[M])}};return d.scopeName=o.scopeName,d}function zg(c){const o=c+"CollectionProvider",[d,s]=mi(o),[h,T]=d(o,{collectionRef:{current:null},itemMap:new Map}),M=$=>{const{scope:K,children:ct}=$,gt=Rl.useRef(null),et=Rl.useRef(new Map).current;return S.jsx(h,{scope:K,itemMap:et,collectionRef:gt,children:ct})};M.displayName=o;const R=c+"CollectionSlot",E=oi(R),g=Rl.forwardRef(($,K)=>{const{scope:ct,children:gt}=$,et=T(R,ct),ft=Qn(K,et.collectionRef);return S.jsx(E,{ref:ft,children:gt})});g.displayName=R;const z=c+"CollectionItemSlot",H="data-radix-collection-item",Y=oi(z),tt=Rl.forwardRef(($,K)=>{const{scope:ct,children:gt,...et}=$,ft=Rl.useRef(null),j=Qn(K,ft),lt=T(z,ct);return Rl.useEffect(()=>(lt.itemMap.set(ft,{ref:ft,...et}),()=>void lt.itemMap.delete(ft))),S.jsx(Y,{[H]:"",ref:j,children:gt})});tt.displayName=z;function F($){const K=T(c+"CollectionConsumer",$);return Rl.useCallback(()=>{const gt=K.collectionRef.current;if(!gt)return[];const et=Array.from(gt.querySelectorAll(`[${H}]`));return Array.from(K.itemMap.values()).sort((lt,bt)=>et.indexOf(lt.ref.current)-et.indexOf(bt.ref.current))},[K.collectionRef,K.itemMap])}return[{Provider:M,Slot:g,ItemSlot:tt},F,s]}var ri=globalThis!=null&&globalThis.document?q.useLayoutEffect:()=>{},Ng=hm[" useId ".trim().toString()]||(()=>{}),Og=0;function Um(c){const[o,d]=q.useState(Ng());return ri(()=>{d(s=>s??String(Og++))},[c]),c||(o?`radix-${o}`:"")}function Rg(c){const o=q.useRef(c);return q.useEffect(()=>{o.current=c}),q.useMemo(()=>(...d)=>{var s;return(s=o.current)==null?void 0:s.call(o,...d)},[])}var Dg=hm[" useInsertionEffect ".trim().toString()]||ri;function jm({prop:c,defaultProp:o,onChange:d=()=>{},caller:s}){const[h,T,M]=Ug({defaultProp:o,onChange:d}),R=c!==void 0,E=R?c:h;{const z=q.useRef(c!==void 0);q.useEffect(()=>{const H=z.current;H!==R&&console.warn(`${s} is changing from ${H?"controlled":"uncontrolled"} to ${R?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),z.current=R},[R,s])}const g=q.useCallback(z=>{var H;if(R){const Y=jg(z)?z(c):z;Y!==c&&((H=M.current)==null||H.call(M,Y))}else T(z)},[R,c,T,M]);return[E,g]}function Ug({defaultProp:c,onChange:o}){const[d,s]=q.useState(c),h=q.useRef(d),T=q.useRef(o);return Dg(()=>{T.current=o},[o]),q.useEffect(()=>{var M;h.current!==d&&((M=T.current)==null||M.call(T,d),h.current=d)},[d,h]),[d,s,T]}function jg(c){return typeof c=="function"}var Cg=q.createContext(void 0);function Cm(c){const o=q.useContext(Cg);return c||o||"ltr"}var Bf="rovingFocusGroup.onEntryFocus",wg={bubbles:!1,cancelable:!0},Ln="RovingFocusGroup",[Qf,wm,Hg]=zg(Ln),[qg,Hm]=mi(Ln,[Hg]),[Bg,Gg]=qg(Ln),qm=q.forwardRef((c,o)=>S.jsx(Qf.Provider,{scope:c.__scopeRovingFocusGroup,children:S.jsx(Qf.Slot,{scope:c.__scopeRovingFocusGroup,children:S.jsx(Yg,{...c,ref:o})})}));qm.displayName=Ln;var Yg=q.forwardRef((c,o)=>{const{__scopeRovingFocusGroup:d,orientation:s,loop:h=!1,dir:T,currentTabStopId:M,defaultCurrentTabStopId:R,onCurrentTabStopIdChange:E,onEntryFocus:g,preventScrollOnEntryFocus:z=!1,...H}=c,Y=q.useRef(null),tt=Qn(o,Y),F=Cm(T),[$,K]=jm({prop:M,defaultProp:R??null,onChange:E,caller:Ln}),[ct,gt]=q.useState(!1),et=Rg(g),ft=wm(d),j=q.useRef(!1),[lt,bt]=q.useState(0);return q.useEffect(()=>{const G=Y.current;if(G)return G.addEventListener(Bf,et),()=>G.removeEventListener(Bf,et)},[et]),S.jsx(Bg,{scope:d,orientation:s,dir:F,loop:h,currentTabStopId:$,onItemFocus:q.useCallback(G=>K(G),[K]),onItemShiftTab:q.useCallback(()=>gt(!0),[]),onFocusableItemAdd:q.useCallback(()=>bt(G=>G+1),[]),onFocusableItemRemove:q.useCallback(()=>bt(G=>G-1),[]),children:S.jsx(el.div,{tabIndex:ct||lt===0?-1:0,"data-orientation":s,...H,ref:tt,style:{outline:"none",...c.style},onMouseDown:tl(c.onMouseDown,()=>{j.current=!0}),onFocus:tl(c.onFocus,G=>{const Nt=!j.current;if(G.target===G.currentTarget&&Nt&&!ct){const he=new CustomEvent(Bf,wg);if(G.currentTarget.dispatchEvent(he),!he.defaultPrevented){const Jt=ft().filter(_=>_.focusable),Rt=Jt.find(_=>_.active),ye=Jt.find(_=>_.id===$),zt=[Rt,ye,...Jt].filter(Boolean).map(_=>_.ref.current);Ym(zt,z)}}j.current=!1}),onBlur:tl(c.onBlur,()=>gt(!1))})})}),Bm="RovingFocusGroupItem",Gm=q.forwardRef((c,o)=>{const{__scopeRovingFocusGroup:d,focusable:s=!0,active:h=!1,tabStopId:T,children:M,...R}=c,E=Um(),g=T||E,z=Gg(Bm,d),H=z.currentTabStopId===g,Y=wm(d),{onFocusableItemAdd:tt,onFocusableItemRemove:F,currentTabStopId:$}=z;return q.useEffect(()=>{if(s)return tt(),()=>F()},[s,tt,F]),S.jsx(Qf.ItemSlot,{scope:d,id:g,focusable:s,active:h,children:S.jsx(el.span,{tabIndex:H?0:-1,"data-orientation":z.orientation,...R,ref:o,onMouseDown:tl(c.onMouseDown,K=>{s?z.onItemFocus(g):K.preventDefault()}),onFocus:tl(c.onFocus,()=>z.onItemFocus(g)),onKeyDown:tl(c.onKeyDown,K=>{if(K.key==="Tab"&&K.shiftKey){z.onItemShiftTab();return}if(K.target!==K.currentTarget)return;const ct=Qg(K,z.orientation,z.dir);if(ct!==void 0){if(K.metaKey||K.ctrlKey||K.altKey||K.shiftKey)return;K.preventDefault();let et=Y().filter(ft=>ft.focusable).map(ft=>ft.ref.current);if(ct==="last")et.reverse();else if(ct==="prev"||ct==="next"){ct==="prev"&&et.reverse();const ft=et.indexOf(K.currentTarget);et=z.loop?Lg(et,ft+1):et.slice(ft+1)}setTimeout(()=>Ym(et))}}),children:typeof M=="function"?M({isCurrentTabStop:H,hasTabStop:$!=null}):M})})});Gm.displayName=Bm;var Xg={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Vg(c,o){return o!=="rtl"?c:c==="ArrowLeft"?"ArrowRight":c==="ArrowRight"?"ArrowLeft":c}function Qg(c,o,d){const s=Vg(c.key,d);if(!(o==="vertical"&&["ArrowLeft","ArrowRight"].includes(s))&&!(o==="horizontal"&&["ArrowUp","ArrowDown"].includes(s)))return Xg[s]}function Ym(c,o=!1){const d=document.activeElement;for(const s of c)if(s===d||(s.focus({preventScroll:o}),document.activeElement!==d))return}function Lg(c,o){return c.map((d,s)=>c[(o+s)%c.length])}var Zg=qm,Kg=Gm;function kg(c,o){return q.useReducer((d,s)=>o[d][s]??d,c)}var Xm=c=>{const{present:o,children:d}=c,s=Jg(o),h=typeof d=="function"?d({present:s.isPresent}):q.Children.only(d),T=Qn(s.ref,$g(h));return typeof d=="function"||s.isPresent?q.cloneElement(h,{ref:T}):null};Xm.displayName="Presence";function Jg(c){const[o,d]=q.useState(),s=q.useRef(null),h=q.useRef(c),T=q.useRef("none"),M=c?"mounted":"unmounted",[R,E]=kg(M,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return q.useEffect(()=>{const g=ci(s.current);T.current=R==="mounted"?g:"none"},[R]),ri(()=>{const g=s.current,z=h.current;if(z!==c){const Y=T.current,tt=ci(g);c?E("MOUNT"):tt==="none"||(g==null?void 0:g.display)==="none"?E("UNMOUNT"):E(z&&Y!==tt?"ANIMATION_OUT":"UNMOUNT"),h.current=c}},[c,E]),ri(()=>{if(o){let g;const z=o.ownerDocument.defaultView??window,H=tt=>{const $=ci(s.current).includes(tt.animationName);if(tt.target===o&&$&&(E("ANIMATION_END"),!h.current)){const K=o.style.animationFillMode;o.style.animationFillMode="forwards",g=z.setTimeout(()=>{o.style.animationFillMode==="forwards"&&(o.style.animationFillMode=K)})}},Y=tt=>{tt.target===o&&(T.current=ci(s.current))};return o.addEventListener("animationstart",Y),o.addEventListener("animationcancel",H),o.addEventListener("animationend",H),()=>{z.clearTimeout(g),o.removeEventListener("animationstart",Y),o.removeEventListener("animationcancel",H),o.removeEventListener("animationend",H)}}else E("ANIMATION_END")},[o,E]),{isPresent:["mounted","unmountSuspended"].includes(R),ref:q.useCallback(g=>{s.current=g?getComputedStyle(g):null,d(g)},[])}}function ci(c){return(c==null?void 0:c.animationName)||"none"}function $g(c){var s,h;let o=(s=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:s.get,d=o&&"isReactWarning"in o&&o.isReactWarning;return d?c.ref:(o=(h=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:h.get,d=o&&"isReactWarning"in o&&o.isReactWarning,d?c.props.ref:c.props.ref||c.ref)}var vi="Tabs",[Wg,R0]=mi(vi,[Hm]),Vm=Hm(),[Fg,Kf]=Wg(vi),Qm=q.forwardRef((c,o)=>{const{__scopeTabs:d,value:s,onValueChange:h,defaultValue:T,orientation:M="horizontal",dir:R,activationMode:E="automatic",...g}=c,z=Cm(R),[H,Y]=jm({prop:s,onChange:h,defaultProp:T??"",caller:vi});return S.jsx(Fg,{scope:d,baseId:Um(),value:H,onValueChange:Y,orientation:M,dir:z,activationMode:E,children:S.jsx(el.div,{dir:z,"data-orientation":M,...g,ref:o})})});Qm.displayName=vi;var Lm="TabsList",Zm=q.forwardRef((c,o)=>{const{__scopeTabs:d,loop:s=!0,...h}=c,T=Kf(Lm,d),M=Vm(d);return S.jsx(Zg,{asChild:!0,...M,orientation:T.orientation,dir:T.dir,loop:s,children:S.jsx(el.div,{role:"tablist","aria-orientation":T.orientation,...h,ref:o})})});Zm.displayName=Lm;var Km="TabsTrigger",km=q.forwardRef((c,o)=>{const{__scopeTabs:d,value:s,disabled:h=!1,...T}=c,M=Kf(Km,d),R=Vm(d),E=Wm(M.baseId,s),g=Fm(M.baseId,s),z=s===M.value;return S.jsx(Kg,{asChild:!0,...R,focusable:!h,active:z,children:S.jsx(el.button,{type:"button",role:"tab","aria-selected":z,"aria-controls":g,"data-state":z?"active":"inactive","data-disabled":h?"":void 0,disabled:h,id:E,...T,ref:o,onMouseDown:tl(c.onMouseDown,H=>{!h&&H.button===0&&H.ctrlKey===!1?M.onValueChange(s):H.preventDefault()}),onKeyDown:tl(c.onKeyDown,H=>{[" ","Enter"].includes(H.key)&&M.onValueChange(s)}),onFocus:tl(c.onFocus,()=>{const H=M.activationMode!=="manual";!z&&!h&&H&&M.onValueChange(s)})})})});km.displayName=Km;var Jm="TabsContent",$m=q.forwardRef((c,o)=>{const{__scopeTabs:d,value:s,forceMount:h,children:T,...M}=c,R=Kf(Jm,d),E=Wm(R.baseId,s),g=Fm(R.baseId,s),z=s===R.value,H=q.useRef(z);return q.useEffect(()=>{const Y=requestAnimationFrame(()=>H.current=!1);return()=>cancelAnimationFrame(Y)},[]),S.jsx(Xm,{present:h||z,children:({present:Y})=>S.jsx(el.div,{"data-state":z?"active":"inactive","data-orientation":R.orientation,role:"tabpanel","aria-labelledby":E,hidden:!Y,id:g,tabIndex:0,...M,ref:o,style:{...c.style,animationDuration:H.current?"0s":void 0},children:Y&&T})})});$m.displayName=Jm;function Wm(c,o){return`${c}-trigger-${o}`}function Fm(c,o){return`${c}-content-${o}`}var Ig=Qm,Pg=Zm,t0=km,e0=$m;function l0({className:c,...o}){return S.jsx(Ig,{"data-slot":"tabs",className:ne("flex flex-col gap-2",c),...o})}function a0({className:c,...o}){return S.jsx(Pg,{"data-slot":"tabs-list",className:ne("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",c),...o})}function fi({className:c,...o}){return S.jsx(t0,{"data-slot":"tabs-trigger",className:ne("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",c),...o})}function si({className:c,...o}){return S.jsx(e0,{"data-slot":"tabs-content",className:ne("flex-1 outline-none",c),...o})}const n0=Sm("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function fm({className:c,variant:o,asChild:d=!1,...s}){const h=d?gm:"span";return S.jsx(h,{"data-slot":"badge",className:ne(n0({variant:o}),c),...s})}var kf="Progress",Jf=100,[u0,D0]=mi(kf),[i0,c0]=u0(kf),Im=q.forwardRef((c,o)=>{const{__scopeProgress:d,value:s=null,max:h,getValueLabel:T=f0,...M}=c;(h||h===0)&&!sm(h)&&console.error(s0(`${h}`,"Progress"));const R=sm(h)?h:Jf;s!==null&&!om(s,R)&&console.error(o0(`${s}`,"Progress"));const E=om(s,R)?s:null,g=di(E)?T(E,R):void 0;return S.jsx(i0,{scope:d,value:E,max:R,children:S.jsx(el.div,{"aria-valuemax":R,"aria-valuemin":0,"aria-valuenow":di(E)?E:void 0,"aria-valuetext":g,role:"progressbar","data-state":ev(E,R),"data-value":E??void 0,"data-max":R,...M,ref:o})})});Im.displayName=kf;var Pm="ProgressIndicator",tv=q.forwardRef((c,o)=>{const{__scopeProgress:d,...s}=c,h=c0(Pm,d);return S.jsx(el.div,{"data-state":ev(h.value,h.max),"data-value":h.value??void 0,"data-max":h.max,...s,ref:o})});tv.displayName=Pm;function f0(c,o){return`${Math.round(c/o*100)}%`}function ev(c,o){return c==null?"indeterminate":c===o?"complete":"loading"}function di(c){return typeof c=="number"}function sm(c){return di(c)&&!isNaN(c)&&c>0}function om(c,o){return di(c)&&!isNaN(c)&&c<=o&&c>=0}function s0(c,o){return`Invalid prop \`max\` of value \`${c}\` supplied to \`${o}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Jf}\`.`}function o0(c,o){return`Invalid prop \`value\` of value \`${c}\` supplied to \`${o}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Jf} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var r0=Im,d0=tv;function m0({className:c,value:o,...d}){return S.jsx(r0,{"data-slot":"progress",className:ne("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",c),...d,children:S.jsx(d0,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(o||0)}%)`}})})}/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v0=c=>c.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),h0=c=>c.replace(/^([A-Z])|[\s-_]+(\w)/g,(o,d,s)=>s?s.toUpperCase():d.toLowerCase()),rm=c=>{const o=h0(c);return o.charAt(0).toUpperCase()+o.slice(1)},lv=(...c)=>c.filter((o,d,s)=>!!o&&o.trim()!==""&&s.indexOf(o)===d).join(" ").trim(),y0=c=>{for(const o in c)if(o.startsWith("aria-")||o==="role"||o==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var g0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b0=q.forwardRef(({color:c="currentColor",size:o=24,strokeWidth:d=2,absoluteStrokeWidth:s,className:h="",children:T,iconNode:M,...R},E)=>q.createElement("svg",{ref:E,...g0,width:o,height:o,stroke:c,strokeWidth:s?Number(d)*24/Number(o):d,className:lv("lucide",h),...!T&&!y0(R)&&{"aria-hidden":"true"},...R},[...M.map(([g,z])=>q.createElement(g,z)),...Array.isArray(T)?T:[T]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Il=(c,o)=>{const d=q.forwardRef(({className:s,...h},T)=>q.createElement(b0,{ref:T,iconNode:o,className:lv(`lucide-${v0(rm(c))}`,`lucide-${c}`,s),...h}));return d.displayName=rm(c),d};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p0=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],S0=Il("chart-column",p0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x0=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],T0=Il("clock",x0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A0=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],dm=Il("database",A0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E0=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],mm=Il("file-text",E0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _0=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],M0=Il("funnel",_0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z0=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Gf=Il("search",z0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N0=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]],vm=Il("upload",N0),Vn="http://localhost:5000/api";function O0(){const[c,o]=q.useState([]),[d,s]=q.useState([]),[h,T]=q.useState({}),[M,R]=q.useState(!1),[E,g]=q.useState(""),[z,H]=q.useState("upload_date"),[Y,tt]=q.useState("desc");q.useEffect(()=>{F(),$()},[z,Y]);const F=async()=>{try{R(!0);const lt=await(await fetch(`${Vn}/documents?sort_by=${z}&sort_order=${Y}`)).json();o(lt.documents||[])}catch(j){console.error("Error fetching documents:",j)}finally{R(!1)}},$=async()=>{try{const lt=await(await fetch(`${Vn}/statistics`)).json();T(lt)}catch(j){console.error("Error fetching statistics:",j)}},K=async j=>{const lt=j.target.files[0];if(!lt)return;const bt=new FormData;bt.append("file",lt);try{R(!0);const G=await fetch(`${Vn}/upload`,{method:"POST",body:bt});if(G.ok)await F(),await $(),j.target.value="";else{const Nt=await G.json();alert(`Upload failed: ${Nt.error}`)}}catch(G){console.error("Error uploading file:",G),alert("Upload failed. Please try again.")}finally{R(!1)}},ct=async()=>{if(!E.trim()){s([]);return}try{R(!0);const lt=await(await fetch(`${Vn}/search`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({keywords:E})})).json();s(lt.documents||[])}catch(j){console.error("Error searching documents:",j)}finally{R(!1)}},gt=async()=>{try{R(!0),(await fetch(`${Vn}/classify`,{method:"POST"})).ok&&(await F(),await $(),alert("Documents classified successfully!"))}catch(j){console.error("Error classifying documents:",j)}finally{R(!1)}},et=j=>{if(j===0)return"0 Bytes";const lt=1024,bt=["Bytes","KB","MB","GB"],G=Math.floor(Math.log(j)/Math.log(lt));return parseFloat((j/Math.pow(lt,G)).toFixed(2))+" "+bt[G]},ft=j=>new Date(j).toLocaleDateString();return S.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:S.jsxs("div",{className:"container mx-auto p-6",children:[S.jsxs("div",{className:"mb-8 text-center",children:[S.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"Document Analytics Service"}),S.jsx("p",{className:"text-lg text-gray-600",children:"Upload, search, sort, and classify your documents with AI-powered analytics"})]}),S.jsxs(l0,{defaultValue:"upload",className:"space-y-6",children:[S.jsxs(a0,{className:"grid w-full grid-cols-4",children:[S.jsxs(fi,{value:"upload",className:"flex items-center gap-2",children:[S.jsx(vm,{className:"w-4 h-4"}),"Upload"]}),S.jsxs(fi,{value:"search",className:"flex items-center gap-2",children:[S.jsx(Gf,{className:"w-4 h-4"}),"Search"]}),S.jsxs(fi,{value:"documents",className:"flex items-center gap-2",children:[S.jsx(mm,{className:"w-4 h-4"}),"Documents"]}),S.jsxs(fi,{value:"analytics",className:"flex items-center gap-2",children:[S.jsx(S0,{className:"w-4 h-4"}),"Analytics"]})]}),S.jsx(si,{value:"upload",children:S.jsxs(Ie,{children:[S.jsxs(Wl,{children:[S.jsxs(Fl,{className:"flex items-center gap-2",children:[S.jsx(vm,{className:"w-5 h-5"}),"Upload Documents"]}),S.jsx(qf,{children:"Upload PDF or DOCX files for processing and analysis"})]}),S.jsxs(Pe,{className:"space-y-4",children:[S.jsxs("div",{className:"grid w-full max-w-sm items-center gap-1.5",children:[S.jsx(cm,{htmlFor:"file",children:"Choose File"}),S.jsx(im,{id:"file",type:"file",accept:".pdf,.docx",onChange:K,disabled:M})]}),M&&S.jsxs("div",{className:"flex items-center gap-2",children:[S.jsx(m0,{value:50,className:"w-full"}),S.jsx("span",{className:"text-sm text-gray-600",children:"Processing..."})]})]})]})}),S.jsx(si,{value:"search",children:S.jsxs(Ie,{children:[S.jsxs(Wl,{children:[S.jsxs(Fl,{className:"flex items-center gap-2",children:[S.jsx(Gf,{className:"w-5 h-5"}),"Search Documents"]}),S.jsx(qf,{children:"Search through your documents using keywords"})]}),S.jsxs(Pe,{className:"space-y-4",children:[S.jsxs("div",{className:"flex gap-2",children:[S.jsx(im,{placeholder:"Enter keywords to search...",value:E,onChange:j=>g(j.target.value),onKeyPress:j=>j.key==="Enter"&&ct()}),S.jsxs(um,{onClick:ct,disabled:M,children:[S.jsx(Gf,{className:"w-4 h-4 mr-2"}),"Search"]})]}),d.length>0&&S.jsxs("div",{className:"space-y-4",children:[S.jsxs("h3",{className:"text-lg font-semibold",children:["Search Results (",d.length,")"]}),d.map(j=>S.jsx(Ie,{className:"hover:shadow-md transition-shadow",children:S.jsxs(Pe,{className:"p-4",children:[S.jsxs("div",{className:"flex justify-between items-start mb-2",children:[S.jsx("h4",{className:"font-semibold text-lg",children:j.title}),S.jsx(fm,{variant:"secondary",children:j.classification})]}),S.jsx("p",{className:"text-sm text-gray-600 mb-2",children:j.filename}),j.highlighted_content&&S.jsx("div",{className:"text-sm bg-gray-50 p-3 rounded border max-h-32 overflow-y-auto",dangerouslySetInnerHTML:{__html:j.highlighted_content.substring(0,500)+"..."}})]})},j.id))]})]})]})}),S.jsx(si,{value:"documents",children:S.jsxs(Ie,{children:[S.jsxs(Wl,{children:[S.jsxs(Fl,{className:"flex items-center gap-2",children:[S.jsx(mm,{className:"w-5 h-5"}),"Document Library"]}),S.jsx(qf,{children:"View and manage your uploaded documents"})]}),S.jsxs(Pe,{className:"space-y-4",children:[S.jsxs("div",{className:"flex gap-4 items-center",children:[S.jsxs("div",{className:"flex items-center gap-2",children:[S.jsx(M0,{className:"w-4 h-4"}),S.jsx(cm,{children:"Sort by:"}),S.jsxs("select",{value:z,onChange:j=>H(j.target.value),className:"border rounded px-2 py-1",children:[S.jsx("option",{value:"upload_date",children:"Upload Date"}),S.jsx("option",{value:"title",children:"Title"}),S.jsx("option",{value:"file_size",children:"File Size"})]}),S.jsxs("select",{value:Y,onChange:j=>tt(j.target.value),className:"border rounded px-2 py-1",children:[S.jsx("option",{value:"desc",children:"Descending"}),S.jsx("option",{value:"asc",children:"Ascending"})]})]}),S.jsx(um,{onClick:gt,variant:"outline",disabled:M,children:"Classify All Documents"})]}),S.jsx("div",{className:"grid gap-4",children:c.map(j=>S.jsx(Ie,{className:"hover:shadow-md transition-shadow",children:S.jsx(Pe,{className:"p-4",children:S.jsxs("div",{className:"flex justify-between items-start",children:[S.jsxs("div",{className:"flex-1",children:[S.jsx("h4",{className:"font-semibold text-lg mb-1",children:j.title}),S.jsx("p",{className:"text-sm text-gray-600 mb-2",children:j.filename}),S.jsxs("div",{className:"flex gap-4 text-sm text-gray-500",children:[S.jsxs("span",{children:["Size: ",et(j.file_size)]}),S.jsxs("span",{children:["Uploaded: ",ft(j.upload_date)]}),j.author&&S.jsxs("span",{children:["Author: ",j.author]})]})]}),S.jsxs("div",{className:"flex flex-col items-end gap-2",children:[S.jsx(fm,{variant:j.classification?"default":"secondary",children:j.classification||"Unclassified"}),j.classification_confidence&&S.jsxs("span",{className:"text-xs text-gray-500",children:[Math.round(j.classification_confidence*100),"% confidence"]})]})]})})},j.id))})]})]})}),S.jsx(si,{value:"analytics",children:S.jsxs("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[S.jsxs(Ie,{children:[S.jsxs(Wl,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[S.jsx(Fl,{className:"text-sm font-medium",children:"Total Documents"}),S.jsx(dm,{className:"h-4 w-4 text-muted-foreground"})]}),S.jsx(Pe,{children:S.jsx("div",{className:"text-2xl font-bold",children:h.total_documents||0})})]}),S.jsxs(Ie,{children:[S.jsxs(Wl,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[S.jsx(Fl,{className:"text-sm font-medium",children:"Total Storage"}),S.jsx(dm,{className:"h-4 w-4 text-muted-foreground"})]}),S.jsx(Pe,{children:S.jsxs("div",{className:"text-2xl font-bold",children:[h.total_size_mb||0," MB"]})})]}),S.jsxs(Ie,{children:[S.jsxs(Wl,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[S.jsx(Fl,{className:"text-sm font-medium",children:"Avg Search Time"}),S.jsx(T0,{className:"h-4 w-4 text-muted-foreground"})]}),S.jsx(Pe,{children:S.jsxs("div",{className:"text-2xl font-bold",children:[h.average_search_time||0,"s"]})})]}),S.jsxs(Ie,{className:"md:col-span-2 lg:col-span-3",children:[S.jsx(Wl,{children:S.jsx(Fl,{children:"Document Classification Distribution"})}),S.jsx(Pe,{children:S.jsx("div",{className:"space-y-2",children:h.classification_distribution&&Object.entries(h.classification_distribution).map(([j,lt])=>S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsx("span",{className:"text-sm font-medium",children:j}),S.jsxs("div",{className:"flex items-center gap-2",children:[S.jsx("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:S.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${lt/(h.total_documents||1)*100}%`}})}),S.jsx("span",{className:"text-sm text-gray-600",children:lt})]})]},j))})})]})]})})]})]})})}wy.createRoot(document.getElementById("root")).render(S.jsx(q.StrictMode,{children:S.jsx(O0,{})}));
