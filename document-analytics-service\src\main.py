import os
import sys
import logging
from datetime import datetime

# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory, jsonify
from flask_cors import CORS
from src.config import config
from src.models.user import db
from src.models.document import Document, SearchLog
from src.routes.user import user_bp
from src.routes.document import document_bp
from src.routes.analytics import analytics_bp
from src.routes.scraping import scraping_bp
from src.services.analytics_service import AnalyticsService
from src.services.classification_service import DocumentClassificationService
from src.services.search_service import AdvancedSearchService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)

# Ensure logs directory exists
os.makedirs('logs', exist_ok=True)

def create_app(config_name='development'):
    """Application factory pattern"""
    app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))

    # Load configuration
    app.config.from_object(config[config_name])

    # Initialize extensions
    CORS(app)
    db.init_app(app)

    # Initialize services
    app.analytics_service = AnalyticsService()
    app.classification_service = DocumentClassificationService()
    app.search_service = AdvancedSearchService()

    # Register blueprints
    app.register_blueprint(user_bp, url_prefix='/api')
    app.register_blueprint(document_bp, url_prefix='/api')
    app.register_blueprint(analytics_bp, url_prefix='/api')
    app.register_blueprint(scraping_bp, url_prefix='/api')

    # Create database tables
    with app.app_context():
        db.create_all()

        # Index existing documents for search
        try:
            documents = Document.query.all()
            if documents:
                doc_data = [doc.to_dict() for doc in documents]
                app.search_service.index_documents(doc_data)
                logging.info(f"Indexed {len(documents)} documents for search")
        except Exception as e:
            logging.error(f"Error indexing documents: {e}")

    return app

# Create app instance
config_name = os.environ.get('FLASK_ENV', 'development')
app = create_app(config_name)

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
            return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
