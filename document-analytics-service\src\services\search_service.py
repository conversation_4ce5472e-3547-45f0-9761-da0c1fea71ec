import re
import logging
from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.stem import PorterStemmer
import math

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Data class for search results"""
    document_id: int
    title: str
    filename: str
    relevance_score: float
    highlighted_content: str
    snippet: str
    match_count: int
    classification: Optional[str] = None

@dataclass
class SearchQuery:
    """Data class for search queries"""
    keywords: str
    exact_phrase: bool = False
    case_sensitive: bool = False
    include_title: bool = True
    include_content: bool = True
    classification_filter: Optional[str] = None
    date_range: Optional[Tuple] = None

class AdvancedSearchService:
    """Advanced search service with highlighting and ranking"""
    
    def __init__(self):
        self.stemmer = PorterStemmer()
        self.stop_words = set(stopwords.words('english'))
        self.tfidf_vectorizer = None
        self.document_vectors = None
        self.documents_cache = []
        
    def index_documents(self, documents: List[Dict]) -> None:
        """Index documents for faster searching"""
        try:
            self.documents_cache = documents
            
            # Prepare text corpus for TF-IDF
            corpus = []
            for doc in documents:
                text = self._prepare_document_text(doc)
                corpus.append(text)
            
            # Create TF-IDF vectors
            if corpus:
                self.tfidf_vectorizer = TfidfVectorizer(
                    max_features=5000,
                    stop_words='english',
                    ngram_range=(1, 2),
                    min_df=1,
                    max_df=0.95
                )
                self.document_vectors = self.tfidf_vectorizer.fit_transform(corpus)
                logger.info(f"Indexed {len(documents)} documents")
            
        except Exception as e:
            logger.error(f"Error indexing documents: {e}")
    
    def search(self, query: SearchQuery, limit: int = 20) -> List[SearchResult]:
        """Perform advanced search with ranking and highlighting"""
        try:
            if not self.documents_cache:
                return []
            
            # Parse and process query
            processed_query = self._process_query(query.keywords)
            
            # Get candidate documents
            candidates = self._get_candidate_documents(query)
            
            # Score and rank documents
            scored_results = []
            for doc in candidates:
                score = self._calculate_relevance_score(doc, processed_query, query)
                if score > 0:
                    result = self._create_search_result(doc, processed_query, query, score)
                    scored_results.append(result)
            
            # Sort by relevance score
            scored_results.sort(key=lambda x: x.relevance_score, reverse=True)
            
            return scored_results[:limit]
            
        except Exception as e:
            logger.error(f"Error performing search: {e}")
            return []
    
    def _process_query(self, query: str) -> Dict:
        """Process and analyze the search query"""
        processed = {
            'original': query,
            'tokens': [],
            'stemmed_tokens': [],
            'phrases': [],
            'boolean_operators': []
        }
        
        # Extract quoted phrases
        phrases = re.findall(r'"([^"]*)"', query)
        processed['phrases'] = phrases
        
        # Remove phrases from query for token extraction
        query_without_phrases = re.sub(r'"[^"]*"', '', query)
        
        # Tokenize and process
        tokens = word_tokenize(query_without_phrases.lower())
        processed['tokens'] = [token for token in tokens if token.isalnum() and token not in self.stop_words]
        processed['stemmed_tokens'] = [self.stemmer.stem(token) for token in processed['tokens']]
        
        # Detect boolean operators
        boolean_ops = re.findall(r'\b(AND|OR|NOT)\b', query.upper())
        processed['boolean_operators'] = boolean_ops
        
        return processed
    
    def _get_candidate_documents(self, query: SearchQuery) -> List[Dict]:
        """Get candidate documents based on filters"""
        candidates = self.documents_cache.copy()
        
        # Apply classification filter
        if query.classification_filter:
            candidates = [doc for doc in candidates 
                         if doc.get('classification', '').lower() == query.classification_filter.lower()]
        
        # Apply date range filter
        if query.date_range:
            start_date, end_date = query.date_range
            candidates = [doc for doc in candidates 
                         if self._is_in_date_range(doc.get('upload_date'), start_date, end_date)]
        
        return candidates
    
    def _calculate_relevance_score(self, document: Dict, processed_query: Dict, query: SearchQuery) -> float:
        """Calculate relevance score for a document"""
        score = 0.0
        
        # Get document text
        doc_text = self._prepare_document_text(document)
        doc_title = document.get('title', '').lower()
        
        # Exact phrase matching (highest weight)
        for phrase in processed_query['phrases']:
            if phrase.lower() in doc_text.lower():
                score += 10.0
            if phrase.lower() in doc_title:
                score += 15.0
        
        # Token matching with TF-IDF similarity
        if self.tfidf_vectorizer and self.document_vectors is not None:
            try:
                query_vector = self.tfidf_vectorizer.transform([' '.join(processed_query['tokens'])])
                doc_index = self.documents_cache.index(document)
                similarity = cosine_similarity(query_vector, self.document_vectors[doc_index:doc_index+1])[0][0]
                score += similarity * 5.0
            except:
                pass
        
        # Individual token matching
        for token in processed_query['tokens']:
            # Content matching
            if query.include_content:
                content_matches = len(re.findall(r'\b' + re.escape(token) + r'\b', doc_text, re.IGNORECASE))
                score += content_matches * 0.5
            
            # Title matching (higher weight)
            if query.include_title:
                title_matches = len(re.findall(r'\b' + re.escape(token) + r'\b', doc_title, re.IGNORECASE))
                score += title_matches * 2.0
        
        # Boost score based on document metadata
        if document.get('classification') in ['Academic', 'Research', 'Technical']:
            score *= 1.1  # Slight boost for academic content
        
        return score
    
    def _create_search_result(self, document: Dict, processed_query: Dict, 
                            query: SearchQuery, score: float) -> SearchResult:
        """Create a search result with highlighting"""
        
        # Generate highlighted content
        highlighted_content = self._highlight_content(
            document.get('content_text', ''), 
            processed_query, 
            query
        )
        
        # Generate snippet
        snippet = self._generate_snippet(
            document.get('content_text', ''), 
            processed_query, 
            max_length=200
        )
        
        # Count matches
        match_count = self._count_matches(document, processed_query, query)
        
        return SearchResult(
            document_id=document.get('id'),
            title=document.get('title', ''),
            filename=document.get('filename', ''),
            relevance_score=score,
            highlighted_content=highlighted_content,
            snippet=snippet,
            match_count=match_count,
            classification=document.get('classification')
        )
    
    def _highlight_content(self, content: str, processed_query: Dict, query: SearchQuery) -> str:
        """Highlight search terms in content"""
        if not content:
            return ""
        
        highlighted = content
        
        # Highlight exact phrases first
        for phrase in processed_query['phrases']:
            pattern = re.escape(phrase)
            if not query.case_sensitive:
                highlighted = re.sub(
                    f'({pattern})', 
                    r'<mark class="phrase-match">\1</mark>', 
                    highlighted, 
                    flags=re.IGNORECASE
                )
            else:
                highlighted = re.sub(
                    f'({pattern})', 
                    r'<mark class="phrase-match">\1</mark>', 
                    highlighted
                )
        
        # Highlight individual tokens
        for token in processed_query['tokens']:
            pattern = r'\b' + re.escape(token) + r'\b'
            if not query.case_sensitive:
                highlighted = re.sub(
                    f'({pattern})', 
                    r'<mark class="token-match">\1</mark>', 
                    highlighted, 
                    flags=re.IGNORECASE
                )
            else:
                highlighted = re.sub(
                    f'({pattern})', 
                    r'<mark class="token-match">\1</mark>', 
                    highlighted
                )
        
        return highlighted
    
    def _generate_snippet(self, content: str, processed_query: Dict, max_length: int = 200) -> str:
        """Generate a relevant snippet from the content"""
        if not content:
            return ""
        
        # Find sentences containing query terms
        sentences = sent_tokenize(content)
        relevant_sentences = []
        
        for sentence in sentences:
            sentence_lower = sentence.lower()
            
            # Check for phrase matches
            for phrase in processed_query['phrases']:
                if phrase.lower() in sentence_lower:
                    relevant_sentences.append((sentence, 10))
                    break
            
            # Check for token matches
            token_count = 0
            for token in processed_query['tokens']:
                if re.search(r'\b' + re.escape(token) + r'\b', sentence_lower):
                    token_count += 1
            
            if token_count > 0:
                relevant_sentences.append((sentence, token_count))
        
        if not relevant_sentences:
            # Return first part of content if no matches
            return content[:max_length] + "..." if len(content) > max_length else content
        
        # Sort by relevance and combine
        relevant_sentences.sort(key=lambda x: x[1], reverse=True)
        snippet = ""
        
        for sentence, _ in relevant_sentences:
            if len(snippet + sentence) <= max_length:
                snippet += sentence + " "
            else:
                break
        
        return snippet.strip()
    
    def _count_matches(self, document: Dict, processed_query: Dict, query: SearchQuery) -> int:
        """Count total matches in document"""
        count = 0
        doc_text = self._prepare_document_text(document)
        
        # Count phrase matches
        for phrase in processed_query['phrases']:
            count += len(re.findall(re.escape(phrase), doc_text, re.IGNORECASE))
        
        # Count token matches
        for token in processed_query['tokens']:
            count += len(re.findall(r'\b' + re.escape(token) + r'\b', doc_text, re.IGNORECASE))
        
        return count
    
    def _prepare_document_text(self, document: Dict) -> str:
        """Prepare document text for searching"""
        text_parts = []
        
        if document.get('title'):
            text_parts.append(document['title'])
        
        if document.get('content_text'):
            text_parts.append(document['content_text'])
        
        if document.get('author'):
            text_parts.append(document['author'])
        
        return ' '.join(text_parts)
    
    def _is_in_date_range(self, date_str: str, start_date, end_date) -> bool:
        """Check if date is in range"""
        try:
            from datetime import datetime
            doc_date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return start_date <= doc_date <= end_date
        except:
            return True  # Include if date parsing fails
    
    def get_search_suggestions(self, partial_query: str, limit: int = 5) -> List[str]:
        """Get search suggestions based on indexed content"""
        suggestions = []
        
        if not self.tfidf_vectorizer or len(partial_query) < 2:
            return suggestions
        
        try:
            # Get feature names from TF-IDF vectorizer
            feature_names = self.tfidf_vectorizer.get_feature_names_out()
            
            # Find matching terms
            partial_lower = partial_query.lower()
            matching_terms = [term for term in feature_names 
                            if term.startswith(partial_lower) and len(term) > len(partial_lower)]
            
            # Sort by frequency (TF-IDF scores)
            if matching_terms:
                term_scores = []
                for term in matching_terms:
                    try:
                        term_vector = self.tfidf_vectorizer.transform([term])
                        score = np.sum(term_vector.toarray())
                        term_scores.append((term, score))
                    except:
                        continue
                
                term_scores.sort(key=lambda x: x[1], reverse=True)
                suggestions = [term for term, _ in term_scores[:limit]]
        
        except Exception as e:
            logger.error(f"Error generating suggestions: {e}")
        
        return suggestions
