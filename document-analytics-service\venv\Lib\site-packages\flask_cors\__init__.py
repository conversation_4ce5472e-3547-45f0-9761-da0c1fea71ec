from .decorator import cross_origin
from .extension import CORS
from .version import __version__

__all__ = ["CORS", "__version__", "cross_origin"]

# Set default logging handler to avoid "No handler found" warnings.
import logging
from logging import Null<PERSON>and<PERSON>

# Set initial level to WARN. Users must manually enable logging for
# flask_cors to see our logging.
rootlogger = logging.getLogger(__name__)
rootlogger.addHandler(NullHandler())

if rootlogger.level == logging.NOTSET:
    rootlogger.setLevel(logging.WARN)
