from flask import Blueprint, request, jsonify, current_app
import logging
import os
import tempfile
from datetime import datetime
from src.services.web_scraper import WebScraper, SeleniumScraper, ScrapedDocument
from src.services.cloud_storage import CloudStorageFactory
from src.utils.document_processor import DocumentProcessor
from src.models.document import db, Document

logger = logging.getLogger(__name__)

scraping_bp = Blueprint('scraping', __name__)

@scraping_bp.route('/scraping/scrape-url', methods=['POST'])
def scrape_url():
    """Scrape documents from a given URL"""
    try:
        data = request.get_json()
        
        if not data or 'url' not in data:
            return jsonify({
                'status': 'error',
                'message': 'URL is required'
            }), 400
        
        url = data['url']
        max_pages = data.get('max_pages', 10)
        file_extensions = data.get('file_extensions', ['pdf', 'docx', 'doc'])
        download_documents = data.get('download_documents', True)
        
        # Validate URL
        if not url.startswith(('http://', 'https://')):
            return jsonify({
                'status': 'error',
                'message': 'Invalid URL format'
            }), 400
        
        # Initialize scraper
        scraper = WebScraper(delay=1.0)
        
        try:
            # Scrape documents
            logger.info(f"Starting scraping of {url}")
            scraped_docs = scraper.scrape_documents_from_url(
                url, 
                max_pages=max_pages, 
                file_extensions=file_extensions
            )
            
            if not scraped_docs:
                return jsonify({
                    'status': 'success',
                    'message': 'No documents found',
                    'data': {
                        'scraped_count': 0,
                        'processed_count': 0,
                        'documents': []
                    }
                }), 200
            
            processed_documents = []
            
            if download_documents:
                # Process and store documents
                processor = DocumentProcessor()
                
                # Create temporary download directory
                with tempfile.TemporaryDirectory() as temp_dir:
                    for scraped_doc in scraped_docs[:20]:  # Limit to 20 documents
                        try:
                            # Download document
                            file_path = scraper.download_document(scraped_doc, temp_dir)
                            
                            if file_path:
                                # Process document
                                processed_doc = process_scraped_document(
                                    scraped_doc, file_path, processor
                                )
                                if processed_doc:
                                    processed_documents.append(processed_doc)
                                    
                        except Exception as e:
                            logger.error(f"Error processing {scraped_doc.url}: {e}")
                            continue
            
            return jsonify({
                'status': 'success',
                'message': f'Successfully scraped {len(scraped_docs)} documents',
                'data': {
                    'scraped_count': len(scraped_docs),
                    'processed_count': len(processed_documents),
                    'documents': [
                        {
                            'url': doc.url,
                            'title': doc.title,
                            'filename': doc.filename,
                            'content_type': doc.content_type,
                            'size': doc.size,
                            'source_page': doc.source_page
                        }
                        for doc in scraped_docs
                    ],
                    'processed_documents': processed_documents
                }
            }), 200
            
        finally:
            scraper.close()
            
    except Exception as e:
        logger.error(f"Error scraping URL: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error scraping URL: {str(e)}'
        }), 500

@scraping_bp.route('/scraping/scrape-academic', methods=['POST'])
def scrape_academic_sources():
    """Scrape documents from academic sources"""
    try:
        data = request.get_json()
        
        if not data or 'query' not in data:
            return jsonify({
                'status': 'error',
                'message': 'Search query is required'
            }), 400
        
        query = data['query']
        max_results = data.get('max_results', 20)
        
        # Initialize scraper
        scraper = WebScraper(delay=2.0)  # Longer delay for academic sources
        
        try:
            # Scrape academic sources
            logger.info(f"Starting academic scraping for query: {query}")
            scraped_docs = scraper.scrape_academic_sources(query, max_results)
            
            return jsonify({
                'status': 'success',
                'message': f'Found {len(scraped_docs)} academic documents',
                'data': {
                    'query': query,
                    'results_count': len(scraped_docs),
                    'documents': [
                        {
                            'url': doc.url,
                            'title': doc.title,
                            'filename': doc.filename,
                            'content_type': doc.content_type,
                            'source_page': doc.source_page
                        }
                        for doc in scraped_docs
                    ]
                }
            }), 200
            
        finally:
            scraper.close()
            
    except Exception as e:
        logger.error(f"Error scraping academic sources: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error scraping academic sources: {str(e)}'
        }), 500

@scraping_bp.route('/scraping/scrape-spa', methods=['POST'])
def scrape_spa():
    """Scrape documents from Single Page Applications using Selenium"""
    try:
        data = request.get_json()
        
        if not data or 'url' not in data:
            return jsonify({
                'status': 'error',
                'message': 'URL is required'
            }), 400
        
        url = data['url']
        wait_selector = data.get('wait_selector')
        
        # Initialize Selenium scraper
        scraper = SeleniumScraper(delay=2.0, headless=True)
        
        try:
            # Scrape SPA
            logger.info(f"Starting SPA scraping of {url}")
            scraped_docs = scraper.scrape_spa_documents(url, wait_selector)
            
            return jsonify({
                'status': 'success',
                'message': f'Found {len(scraped_docs)} documents in SPA',
                'data': {
                    'results_count': len(scraped_docs),
                    'documents': [
                        {
                            'url': doc.url,
                            'title': doc.title,
                            'filename': doc.filename,
                            'content_type': doc.content_type
                        }
                        for doc in scraped_docs
                    ]
                }
            }), 200
            
        finally:
            scraper.close()
            
    except Exception as e:
        logger.error(f"Error scraping SPA: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error scraping SPA: {str(e)}'
        }), 500

@scraping_bp.route('/scraping/download-document', methods=['POST'])
def download_and_process_document():
    """Download and process a specific document"""
    try:
        data = request.get_json()
        
        if not data or 'url' not in data:
            return jsonify({
                'status': 'error',
                'message': 'Document URL is required'
            }), 400
        
        url = data['url']
        title = data.get('title', 'Downloaded Document')
        filename = data.get('filename', 'document.pdf')
        
        # Create ScrapedDocument object
        scraped_doc = ScrapedDocument(
            url=url,
            title=title,
            filename=filename,
            content_type='application/pdf'
        )
        
        # Initialize scraper and processor
        scraper = WebScraper()
        processor = DocumentProcessor()
        
        try:
            # Download and process
            with tempfile.TemporaryDirectory() as temp_dir:
                file_path = scraper.download_document(scraped_doc, temp_dir)
                
                if not file_path:
                    return jsonify({
                        'status': 'error',
                        'message': 'Failed to download document'
                    }), 500
                
                # Process document
                processed_doc = process_scraped_document(scraped_doc, file_path, processor)
                
                if processed_doc:
                    return jsonify({
                        'status': 'success',
                        'message': 'Document downloaded and processed successfully',
                        'data': processed_doc
                    }), 200
                else:
                    return jsonify({
                        'status': 'error',
                        'message': 'Failed to process document'
                    }), 500
                    
        finally:
            scraper.close()
            
    except Exception as e:
        logger.error(f"Error downloading document: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error downloading document: {str(e)}'
        }), 500

@scraping_bp.route('/scraping/status', methods=['GET'])
def get_scraping_status():
    """Get scraping service status and capabilities"""
    try:
        # Check if Selenium is available
        selenium_available = True
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
        except ImportError:
            selenium_available = False
        
        # Check if required packages are available
        packages_status = {
            'requests': True,
            'beautifulsoup4': True,
            'selenium': selenium_available
        }
        
        try:
            import requests
            import bs4
        except ImportError as e:
            packages_status['requests'] = False
            packages_status['beautifulsoup4'] = False
        
        return jsonify({
            'status': 'success',
            'data': {
                'service_available': True,
                'selenium_available': selenium_available,
                'packages_status': packages_status,
                'supported_formats': ['pdf', 'docx', 'doc', 'txt'],
                'max_concurrent_scrapes': 5,
                'rate_limit': '1 request per second'
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting scraping status: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error getting scraping status: {str(e)}'
        }), 500

def process_scraped_document(scraped_doc: ScrapedDocument, file_path: str, processor: DocumentProcessor) -> dict:
    """Process a scraped document and save to database"""
    try:
        # Determine file type
        file_extension = scraped_doc.filename.lower().split('.')[-1]
        
        # Extract content based on file type
        if file_extension == 'pdf':
            title = processor.extract_title_from_pdf(file_path)
            content_text = processor.extract_text_from_pdf(file_path)
            metadata = processor.extract_metadata_from_pdf(file_path)
        elif file_extension in ['docx', 'doc']:
            title = processor.extract_title_from_docx(file_path)
            content_text = processor.extract_text_from_docx(file_path)
            metadata = processor.extract_metadata_from_docx(file_path)
        else:
            # For other file types, use basic processing
            title = scraped_doc.title
            content_text = f"Document from {scraped_doc.url}"
            metadata = {}
        
        # Classify document
        classification_result = current_app.classification_service.classify_document(content_text)
        
        # Get file size
        file_size = os.path.getsize(file_path)
        
        # Create document record
        document = Document(
            title=title or scraped_doc.title,
            filename=scraped_doc.filename,
            file_path=file_path,
            file_size=file_size,
            content_text=content_text,
            classification=classification_result.primary_category,
            classification_confidence=classification_result.confidence,
            author=metadata.get('author'),
            creation_date=metadata.get('creation_date'),
            last_modified=metadata.get('last_modified'),
            source_url=scraped_doc.url
        )
        
        # Save to database
        db.session.add(document)
        db.session.commit()
        
        # Update search index
        try:
            documents = Document.query.all()
            doc_data = [doc.to_dict() for doc in documents]
            current_app.search_service.index_documents(doc_data)
        except Exception as e:
            logger.error(f"Error updating search index: {e}")
        
        return document.to_dict()
        
    except Exception as e:
        logger.error(f"Error processing scraped document: {e}")
        return None
