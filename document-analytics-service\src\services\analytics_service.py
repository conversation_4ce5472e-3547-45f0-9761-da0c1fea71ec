import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import json
import numpy as np
from sqlalchemy import func
from src.models.document import db, Document, SearchLog

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Data class for performance metrics"""
    operation: str
    duration: float
    timestamp: datetime
    document_count: int
    success: bool
    error_message: Optional[str] = None

@dataclass
class SystemStatistics:
    """Data class for comprehensive system statistics"""
    total_documents: int
    total_size_bytes: int
    total_size_mb: float
    average_document_size: float
    document_types: Dict[str, int]
    classification_distribution: Dict[str, int]
    upload_trends: Dict[str, int]
    search_statistics: Dict
    performance_metrics: Dict
    storage_statistics: Dict
    user_activity: Dict

class AnalyticsService:
    """Comprehensive analytics service for document processing system"""
    
    def __init__(self):
        self.performance_log = []
        self.max_performance_entries = 1000  # Keep last 1000 entries
    
    def log_performance(self, operation: str, duration: float, document_count: int = 1, 
                       success: bool = True, error_message: str = None):
        """Log performance metrics for an operation"""
        metric = PerformanceMetrics(
            operation=operation,
            duration=duration,
            timestamp=datetime.now(),
            document_count=document_count,
            success=success,
            error_message=error_message
        )
        
        self.performance_log.append(metric)
        
        # Keep only recent entries
        if len(self.performance_log) > self.max_performance_entries:
            self.performance_log = self.performance_log[-self.max_performance_entries:]
        
        logger.info(f"Performance logged: {operation} took {duration:.3f}s for {document_count} documents")
    
    def get_comprehensive_statistics(self) -> SystemStatistics:
        """Get comprehensive system statistics"""
        try:
            # Document statistics
            doc_stats = self._get_document_statistics()
            
            # Search statistics
            search_stats = self._get_search_statistics()
            
            # Performance metrics
            perf_metrics = self._get_performance_metrics()
            
            # Storage statistics
            storage_stats = self._get_storage_statistics()
            
            # User activity
            user_activity = self._get_user_activity_statistics()
            
            return SystemStatistics(
                total_documents=doc_stats['total_documents'],
                total_size_bytes=doc_stats['total_size_bytes'],
                total_size_mb=doc_stats['total_size_mb'],
                average_document_size=doc_stats['average_document_size'],
                document_types=doc_stats['document_types'],
                classification_distribution=doc_stats['classification_distribution'],
                upload_trends=doc_stats['upload_trends'],
                search_statistics=search_stats,
                performance_metrics=perf_metrics,
                storage_statistics=storage_stats,
                user_activity=user_activity
            )
            
        except Exception as e:
            logger.error(f"Error getting comprehensive statistics: {e}")
            return self._get_empty_statistics()
    
    def _get_document_statistics(self) -> Dict:
        """Get document-related statistics"""
        try:
            # Basic counts
            total_documents = Document.query.count()
            total_size = db.session.query(func.sum(Document.file_size)).scalar() or 0
            
            # Document types
            doc_types = db.session.query(
                func.substr(Document.filename, func.instr(Document.filename, '.') + 1),
                func.count(Document.id)
            ).group_by(func.substr(Document.filename, func.instr(Document.filename, '.') + 1)).all()
            
            document_types = {ext: count for ext, count in doc_types}
            
            # Classification distribution
            classifications = db.session.query(
                Document.classification,
                func.count(Document.id)
            ).group_by(Document.classification).all()
            
            classification_distribution = {
                category or 'Unclassified': count for category, count in classifications
            }
            
            # Upload trends (last 30 days)
            thirty_days_ago = datetime.now() - timedelta(days=30)
            upload_trends = {}
            
            for i in range(30):
                date = thirty_days_ago + timedelta(days=i)
                date_str = date.strftime('%Y-%m-%d')
                
                count = Document.query.filter(
                    func.date(Document.upload_date) == date.date()
                ).count()
                
                upload_trends[date_str] = count
            
            return {
                'total_documents': total_documents,
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'average_document_size': round(total_size / total_documents, 2) if total_documents > 0 else 0,
                'document_types': document_types,
                'classification_distribution': classification_distribution,
                'upload_trends': upload_trends
            }
            
        except Exception as e:
            logger.error(f"Error getting document statistics: {e}")
            return {
                'total_documents': 0,
                'total_size_bytes': 0,
                'total_size_mb': 0,
                'average_document_size': 0,
                'document_types': {},
                'classification_distribution': {},
                'upload_trends': {}
            }
    
    def _get_search_statistics(self) -> Dict:
        """Get search-related statistics"""
        try:
            # Basic search metrics
            total_searches = SearchLog.query.count()
            avg_search_time = db.session.query(func.avg(SearchLog.search_time)).scalar() or 0
            avg_results_count = db.session.query(func.avg(SearchLog.results_count)).scalar() or 0
            
            # Recent searches
            recent_searches = SearchLog.query.order_by(
                SearchLog.timestamp.desc()
            ).limit(10).all()
            
            # Popular search terms
            all_searches = SearchLog.query.all()
            search_terms = []
            for search in all_searches:
                if search.query:
                    terms = search.query.lower().split()
                    search_terms.extend(terms)
            
            popular_terms = Counter(search_terms).most_common(10)
            
            # Search trends (last 7 days)
            seven_days_ago = datetime.now() - timedelta(days=7)
            search_trends = {}
            
            for i in range(7):
                date = seven_days_ago + timedelta(days=i)
                date_str = date.strftime('%Y-%m-%d')
                
                count = SearchLog.query.filter(
                    func.date(SearchLog.timestamp) == date.date()
                ).count()
                
                search_trends[date_str] = count
            
            return {
                'total_searches': total_searches,
                'average_search_time': round(avg_search_time, 4),
                'average_results_count': round(avg_results_count, 1),
                'recent_searches': [
                    {
                        'query': search.query,
                        'results_count': search.results_count,
                        'search_time': search.search_time,
                        'timestamp': search.timestamp.isoformat()
                    }
                    for search in recent_searches
                ],
                'popular_terms': [{'term': term, 'count': count} for term, count in popular_terms],
                'search_trends': search_trends
            }
            
        except Exception as e:
            logger.error(f"Error getting search statistics: {e}")
            return {
                'total_searches': 0,
                'average_search_time': 0,
                'average_results_count': 0,
                'recent_searches': [],
                'popular_terms': [],
                'search_trends': {}
            }
    
    def _get_performance_metrics(self) -> Dict:
        """Get performance metrics"""
        try:
            if not self.performance_log:
                return {
                    'operations': {},
                    'success_rate': 100.0,
                    'average_processing_time': 0,
                    'throughput': 0
                }
            
            # Group by operation
            operations = defaultdict(list)
            for metric in self.performance_log:
                operations[metric.operation].append(metric)
            
            # Calculate metrics per operation
            operation_stats = {}
            total_operations = 0
            successful_operations = 0
            total_duration = 0
            total_documents = 0
            
            for operation, metrics in operations.items():
                durations = [m.duration for m in metrics]
                successes = [m.success for m in metrics]
                doc_counts = [m.document_count for m in metrics]
                
                operation_stats[operation] = {
                    'count': len(metrics),
                    'average_duration': round(np.mean(durations), 3),
                    'min_duration': round(min(durations), 3),
                    'max_duration': round(max(durations), 3),
                    'success_rate': round(sum(successes) / len(successes) * 100, 1),
                    'total_documents_processed': sum(doc_counts)
                }
                
                total_operations += len(metrics)
                successful_operations += sum(successes)
                total_duration += sum(durations)
                total_documents += sum(doc_counts)
            
            # Overall metrics
            overall_success_rate = (successful_operations / total_operations * 100) if total_operations > 0 else 100
            avg_processing_time = (total_duration / total_operations) if total_operations > 0 else 0
            throughput = (total_documents / total_duration) if total_duration > 0 else 0
            
            return {
                'operations': operation_stats,
                'success_rate': round(overall_success_rate, 1),
                'average_processing_time': round(avg_processing_time, 3),
                'throughput': round(throughput, 2)  # documents per second
            }
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {
                'operations': {},
                'success_rate': 100.0,
                'average_processing_time': 0,
                'throughput': 0
            }
    
    def _get_storage_statistics(self) -> Dict:
        """Get storage-related statistics"""
        try:
            # File size distribution
            documents = Document.query.all()
            
            size_ranges = {
                'small': 0,      # < 1MB
                'medium': 0,     # 1MB - 10MB
                'large': 0,      # 10MB - 50MB
                'very_large': 0  # > 50MB
            }
            
            for doc in documents:
                size_mb = doc.file_size / (1024 * 1024)
                if size_mb < 1:
                    size_ranges['small'] += 1
                elif size_mb < 10:
                    size_ranges['medium'] += 1
                elif size_mb < 50:
                    size_ranges['large'] += 1
                else:
                    size_ranges['very_large'] += 1
            
            # Storage growth over time
            storage_growth = {}
            thirty_days_ago = datetime.now() - timedelta(days=30)
            
            for i in range(30):
                date = thirty_days_ago + timedelta(days=i)
                date_str = date.strftime('%Y-%m-%d')
                
                total_size = db.session.query(func.sum(Document.file_size)).filter(
                    Document.upload_date <= date
                ).scalar() or 0
                
                storage_growth[date_str] = round(total_size / (1024 * 1024), 2)  # MB
            
            return {
                'size_distribution': size_ranges,
                'storage_growth': storage_growth
            }
            
        except Exception as e:
            logger.error(f"Error getting storage statistics: {e}")
            return {
                'size_distribution': {'small': 0, 'medium': 0, 'large': 0, 'very_large': 0},
                'storage_growth': {}
            }
    
    def _get_user_activity_statistics(self) -> Dict:
        """Get user activity statistics"""
        try:
            # This is a simplified version - in a real system you'd track user sessions
            
            # Activity by hour (based on search logs and uploads)
            hourly_activity = defaultdict(int)
            
            # Count searches by hour
            searches = SearchLog.query.all()
            for search in searches:
                hour = search.timestamp.hour
                hourly_activity[hour] += 1
            
            # Count uploads by hour
            documents = Document.query.all()
            for doc in documents:
                hour = doc.upload_date.hour
                hourly_activity[hour] += 1
            
            # Convert to list format
            activity_by_hour = [hourly_activity.get(hour, 0) for hour in range(24)]
            
            return {
                'activity_by_hour': activity_by_hour,
                'peak_hour': max(range(24), key=lambda h: hourly_activity.get(h, 0)),
                'total_activities': sum(activity_by_hour)
            }
            
        except Exception as e:
            logger.error(f"Error getting user activity statistics: {e}")
            return {
                'activity_by_hour': [0] * 24,
                'peak_hour': 12,
                'total_activities': 0
            }
    
    def _get_empty_statistics(self) -> SystemStatistics:
        """Return empty statistics in case of error"""
        return SystemStatistics(
            total_documents=0,
            total_size_bytes=0,
            total_size_mb=0,
            average_document_size=0,
            document_types={},
            classification_distribution={},
            upload_trends={},
            search_statistics={},
            performance_metrics={},
            storage_statistics={},
            user_activity={}
        )
    
    def export_analytics_report(self, format: str = 'json') -> str:
        """Export analytics report in specified format"""
        try:
            stats = self.get_comprehensive_statistics()
            
            if format.lower() == 'json':
                return json.dumps(asdict(stats), indent=2, default=str)
            else:
                # Could add other formats like CSV, PDF, etc.
                return json.dumps(asdict(stats), indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Error exporting analytics report: {e}")
            return "{}"
    
    def get_performance_trends(self, operation: str = None, days: int = 7) -> Dict:
        """Get performance trends for specific operations"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Filter performance log
            filtered_metrics = [
                m for m in self.performance_log 
                if m.timestamp >= cutoff_date and (operation is None or m.operation == operation)
            ]
            
            if not filtered_metrics:
                return {'trends': [], 'summary': {}}
            
            # Group by day
            daily_metrics = defaultdict(list)
            for metric in filtered_metrics:
                day = metric.timestamp.strftime('%Y-%m-%d')
                daily_metrics[day].append(metric)
            
            # Calculate daily averages
            trends = []
            for day in sorted(daily_metrics.keys()):
                day_metrics = daily_metrics[day]
                avg_duration = np.mean([m.duration for m in day_metrics])
                success_rate = sum(m.success for m in day_metrics) / len(day_metrics) * 100
                
                trends.append({
                    'date': day,
                    'average_duration': round(avg_duration, 3),
                    'success_rate': round(success_rate, 1),
                    'operation_count': len(day_metrics)
                })
            
            # Summary
            all_durations = [m.duration for m in filtered_metrics]
            all_successes = [m.success for m in filtered_metrics]
            
            summary = {
                'total_operations': len(filtered_metrics),
                'average_duration': round(np.mean(all_durations), 3),
                'success_rate': round(sum(all_successes) / len(all_successes) * 100, 1),
                'duration_trend': 'improving' if len(trends) > 1 and trends[-1]['average_duration'] < trends[0]['average_duration'] else 'stable'
            }
            
            return {
                'trends': trends,
                'summary': summary
            }
            
        except Exception as e:
            logger.error(f"Error getting performance trends: {e}")
            return {'trends': [], 'summary': {}}
