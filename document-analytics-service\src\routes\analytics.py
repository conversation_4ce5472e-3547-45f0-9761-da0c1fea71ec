from flask import Blueprint, request, jsonify, current_app
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

analytics_bp = Blueprint('analytics', __name__)

@analytics_bp.route('/analytics/comprehensive', methods=['GET'])
def get_comprehensive_analytics():
    """Get comprehensive system analytics"""
    try:
        analytics_service = current_app.analytics_service
        stats = analytics_service.get_comprehensive_statistics()
        
        return jsonify({
            'status': 'success',
            'data': {
                'total_documents': stats.total_documents,
                'total_size_bytes': stats.total_size_bytes,
                'total_size_mb': stats.total_size_mb,
                'average_document_size': stats.average_document_size,
                'document_types': stats.document_types,
                'classification_distribution': stats.classification_distribution,
                'upload_trends': stats.upload_trends,
                'search_statistics': stats.search_statistics,
                'performance_metrics': stats.performance_metrics,
                'storage_statistics': stats.storage_statistics,
                'user_activity': stats.user_activity
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting comprehensive analytics: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error retrieving analytics: {str(e)}'
        }), 500

@analytics_bp.route('/analytics/performance', methods=['GET'])
def get_performance_analytics():
    """Get performance analytics"""
    try:
        analytics_service = current_app.analytics_service
        
        # Get query parameters
        operation = request.args.get('operation')
        days = int(request.args.get('days', 7))
        
        trends = analytics_service.get_performance_trends(operation, days)
        
        return jsonify({
            'status': 'success',
            'data': trends
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting performance analytics: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error retrieving performance analytics: {str(e)}'
        }), 500

@analytics_bp.route('/analytics/export', methods=['GET'])
def export_analytics():
    """Export analytics report"""
    try:
        analytics_service = current_app.analytics_service
        
        format_type = request.args.get('format', 'json')
        report = analytics_service.export_analytics_report(format_type)
        
        return jsonify({
            'status': 'success',
            'data': {
                'report': report,
                'format': format_type,
                'generated_at': datetime.now().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error exporting analytics: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error exporting analytics: {str(e)}'
        }), 500

@analytics_bp.route('/analytics/classification-stats', methods=['GET'])
def get_classification_statistics():
    """Get detailed classification statistics"""
    try:
        from src.models.document import Document
        
        classification_service = current_app.classification_service
        
        # Get all documents
        documents = Document.query.all()
        doc_data = [doc.to_dict() for doc in documents]
        
        # Get classification statistics
        stats = classification_service.get_category_statistics(doc_data)
        
        # Get classification tree
        tree = classification_service.get_classification_tree()
        
        return jsonify({
            'status': 'success',
            'data': {
                'statistics': stats,
                'classification_tree': tree,
                'total_documents': len(documents)
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting classification statistics: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error retrieving classification statistics: {str(e)}'
        }), 500

@analytics_bp.route('/analytics/search-insights', methods=['GET'])
def get_search_insights():
    """Get detailed search insights"""
    try:
        from src.models.document import SearchLog
        from sqlalchemy import func
        from collections import Counter
        
        # Get search statistics
        total_searches = SearchLog.query.count()
        
        if total_searches == 0:
            return jsonify({
                'status': 'success',
                'data': {
                    'total_searches': 0,
                    'insights': 'No search data available yet'
                }
            }), 200
        
        # Average metrics
        avg_search_time = SearchLog.query.with_entities(
            func.avg(SearchLog.search_time)
        ).scalar() or 0
        
        avg_results = SearchLog.query.with_entities(
            func.avg(SearchLog.results_count)
        ).scalar() or 0
        
        # Popular search terms
        all_searches = SearchLog.query.all()
        all_terms = []
        
        for search in all_searches:
            if search.query:
                terms = search.query.lower().split()
                all_terms.extend(terms)
        
        popular_terms = Counter(all_terms).most_common(20)
        
        # Search success rate (searches with results > 0)
        successful_searches = SearchLog.query.filter(SearchLog.results_count > 0).count()
        success_rate = (successful_searches / total_searches * 100) if total_searches > 0 else 0
        
        # Recent search patterns
        recent_searches = SearchLog.query.order_by(
            SearchLog.timestamp.desc()
        ).limit(50).all()
        
        return jsonify({
            'status': 'success',
            'data': {
                'total_searches': total_searches,
                'average_search_time': round(avg_search_time, 4),
                'average_results_count': round(avg_results, 1),
                'success_rate': round(success_rate, 1),
                'popular_terms': [{'term': term, 'count': count} for term, count in popular_terms],
                'recent_searches': [
                    {
                        'query': search.query,
                        'results_count': search.results_count,
                        'search_time': search.search_time,
                        'timestamp': search.timestamp.isoformat()
                    }
                    for search in recent_searches
                ]
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting search insights: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error retrieving search insights: {str(e)}'
        }), 500

@analytics_bp.route('/analytics/system-health', methods=['GET'])
def get_system_health():
    """Get system health metrics"""
    try:
        analytics_service = current_app.analytics_service
        
        # Get recent performance metrics
        recent_metrics = analytics_service.performance_log[-100:]  # Last 100 operations
        
        if not recent_metrics:
            return jsonify({
                'status': 'success',
                'data': {
                    'health_status': 'unknown',
                    'message': 'No performance data available'
                }
            }), 200
        
        # Calculate health indicators
        success_rate = sum(1 for m in recent_metrics if m.success) / len(recent_metrics) * 100
        avg_response_time = sum(m.duration for m in recent_metrics) / len(recent_metrics)
        
        # Determine health status
        if success_rate >= 95 and avg_response_time < 5.0:
            health_status = 'excellent'
        elif success_rate >= 90 and avg_response_time < 10.0:
            health_status = 'good'
        elif success_rate >= 80 and avg_response_time < 20.0:
            health_status = 'fair'
        else:
            health_status = 'poor'
        
        # Get error patterns
        errors = [m for m in recent_metrics if not m.success]
        error_types = {}
        for error in errors:
            error_type = error.error_message or 'Unknown error'
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return jsonify({
            'status': 'success',
            'data': {
                'health_status': health_status,
                'success_rate': round(success_rate, 1),
                'average_response_time': round(avg_response_time, 3),
                'total_operations': len(recent_metrics),
                'error_count': len(errors),
                'error_types': error_types,
                'last_updated': datetime.now().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting system health: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error retrieving system health: {str(e)}'
        }), 500

@analytics_bp.route('/analytics/dashboard-summary', methods=['GET'])
def get_dashboard_summary():
    """Get summary data for analytics dashboard"""
    try:
        from src.models.document import Document, SearchLog
        from sqlalchemy import func
        from datetime import datetime, timedelta
        
        # Basic counts
        total_documents = Document.query.count()
        total_searches = SearchLog.query.count()
        
        # Recent activity (last 24 hours)
        yesterday = datetime.now() - timedelta(days=1)
        recent_uploads = Document.query.filter(Document.upload_date >= yesterday).count()
        recent_searches = SearchLog.query.filter(SearchLog.timestamp >= yesterday).count()
        
        # Storage info
        total_size = Document.query.with_entities(func.sum(Document.file_size)).scalar() or 0
        
        # Top classifications
        classifications = Document.query.with_entities(
            Document.classification,
            func.count(Document.id)
        ).group_by(Document.classification).order_by(
            func.count(Document.id).desc()
        ).limit(5).all()
        
        top_classifications = [
            {'category': cat or 'Unclassified', 'count': count}
            for cat, count in classifications
        ]
        
        return jsonify({
            'status': 'success',
            'data': {
                'totals': {
                    'documents': total_documents,
                    'searches': total_searches,
                    'storage_mb': round(total_size / (1024 * 1024), 2)
                },
                'recent_activity': {
                    'uploads_24h': recent_uploads,
                    'searches_24h': recent_searches
                },
                'top_classifications': top_classifications,
                'last_updated': datetime.now().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting dashboard summary: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error retrieving dashboard summary: {str(e)}'
        }), 500
