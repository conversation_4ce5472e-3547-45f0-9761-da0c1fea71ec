import os
import pickle
import logging
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import MultinomialNB
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.pipeline import Pipeline
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import PorterStemmer
import re
import json

logger = logging.getLogger(__name__)

@dataclass
class ClassificationResult:
    """Data class for classification results"""
    primary_category: str
    secondary_category: Optional[str]
    confidence: float
    all_probabilities: Dict[str, float]

class DocumentClassificationService:
    """Advanced document classification service with hierarchical categories"""
    
    def __init__(self, model_path: str = None):
        self.model_path = model_path or "models/document_classifier.pkl"
        self.classifier = None
        self.vectorizer = None
        self.stemmer = PorterStemmer()
        self.stop_words = set(stopwords.words('english'))
        
        # Hierarchical classification tree
        self.classification_tree = {
            'Academic': {
                'subcategories': ['Research Paper', 'Thesis', 'Conference Paper', 'Journal Article'],
                'keywords': ['research', 'study', 'analysis', 'methodology', 'hypothesis', 'experiment', 
                           'university', 'academic', 'scholar', 'peer-reviewed', 'citation', 'bibliography']
            },
            'Business': {
                'subcategories': ['Financial Report', 'Business Plan', 'Marketing Material', 'Strategy Document'],
                'keywords': ['business', 'company', 'revenue', 'profit', 'market', 'strategy', 'financial',
                           'quarterly', 'annual', 'investment', 'stakeholder', 'ROI', 'KPI']
            },
            'Technical': {
                'subcategories': ['Software Documentation', 'API Reference', 'User Manual', 'Technical Specification'],
                'keywords': ['software', 'programming', 'code', 'algorithm', 'system', 'architecture',
                           'database', 'API', 'framework', 'development', 'technical', 'specification']
            },
            'Legal': {
                'subcategories': ['Contract', 'Legal Brief', 'Regulation', 'Policy Document'],
                'keywords': ['legal', 'law', 'contract', 'agreement', 'regulation', 'compliance',
                           'court', 'jurisdiction', 'clause', 'liability', 'terms', 'conditions']
            },
            'Medical': {
                'subcategories': ['Clinical Study', 'Medical Report', 'Pharmaceutical', 'Healthcare Policy'],
                'keywords': ['medical', 'health', 'patient', 'clinical', 'treatment', 'diagnosis',
                           'pharmaceutical', 'drug', 'therapy', 'healthcare', 'hospital', 'doctor']
            },
            'Financial': {
                'subcategories': ['Banking Document', 'Investment Report', 'Insurance Policy', 'Audit Report'],
                'keywords': ['financial', 'banking', 'investment', 'insurance', 'audit', 'accounting',
                           'credit', 'loan', 'portfolio', 'assets', 'liabilities', 'balance sheet']
            },
            'Educational': {
                'subcategories': ['Curriculum', 'Training Material', 'Course Content', 'Assessment'],
                'keywords': ['education', 'learning', 'training', 'curriculum', 'course', 'student',
                           'teacher', 'instruction', 'assessment', 'pedagogy', 'classroom']
            },
            'Government': {
                'subcategories': ['Policy Document', 'Public Report', 'Regulation', 'Administrative'],
                'keywords': ['government', 'public', 'policy', 'administration', 'regulation', 'federal',
                           'state', 'municipal', 'official', 'department', 'agency', 'bureau']
            },
            'Research': {
                'subcategories': ['Scientific Research', 'Market Research', 'Survey Report', 'Data Analysis'],
                'keywords': ['research', 'survey', 'data', 'analysis', 'findings', 'results',
                           'methodology', 'sample', 'statistics', 'correlation', 'hypothesis']
            },
            'General': {
                'subcategories': ['Miscellaneous', 'Other', 'Unclassified'],
                'keywords': ['general', 'miscellaneous', 'other', 'various', 'mixed']
            }
        }
        
        self.primary_categories = list(self.classification_tree.keys())
        self._load_or_create_model()
    
    def _load_or_create_model(self):
        """Load existing model or create a new one"""
        if os.path.exists(self.model_path):
            try:
                with open(self.model_path, 'rb') as f:
                    model_data = pickle.load(f)
                    self.classifier = model_data['classifier']
                    self.vectorizer = model_data['vectorizer']
                logger.info("Loaded existing classification model")
            except Exception as e:
                logger.error(f"Error loading model: {e}")
                self._create_default_model()
        else:
            self._create_default_model()
    
    def _create_default_model(self):
        """Create a default model with synthetic training data"""
        logger.info("Creating default classification model")
        
        # Generate synthetic training data
        training_data = self._generate_training_data()
        
        if training_data:
            texts, labels = zip(*training_data)
            self.train_model(list(texts), list(labels))
    
    def _generate_training_data(self) -> List[Tuple[str, str]]:
        """Generate synthetic training data based on keywords"""
        training_data = []
        
        for category, info in self.classification_tree.items():
            keywords = info['keywords']
            
            # Create multiple training examples per category
            for i in range(10):  # 10 examples per category
                # Combine random keywords to create synthetic documents
                selected_keywords = np.random.choice(keywords, size=min(5, len(keywords)), replace=False)
                
                # Create synthetic text
                text = f"This document discusses {' and '.join(selected_keywords[:3])}. "
                text += f"The main focus is on {selected_keywords[0]} with emphasis on {selected_keywords[1]}. "
                text += f"Key aspects include {', '.join(selected_keywords[2:])}."
                
                training_data.append((text, category))
        
        return training_data
    
    def train_model(self, texts: List[str], labels: List[str], test_size: float = 0.2):
        """Train the classification model"""
        try:
            logger.info(f"Training model with {len(texts)} documents")
            
            # Preprocess texts
            processed_texts = [self._preprocess_text(text) for text in texts]
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                processed_texts, labels, test_size=test_size, random_state=42, stratify=labels
            )
            
            # Create pipeline with multiple classifiers
            classifiers = {
                'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
                'naive_bayes': MultinomialNB(),
                'svm': SVC(probability=True, random_state=42),
                'logistic_regression': LogisticRegression(random_state=42, max_iter=1000)
            }
            
            best_score = 0
            best_classifier = None
            
            # Test different classifiers
            for name, clf in classifiers.items():
                pipeline = Pipeline([
                    ('tfidf', TfidfVectorizer(
                        max_features=5000,
                        stop_words='english',
                        ngram_range=(1, 2),
                        min_df=1,
                        max_df=0.95
                    )),
                    ('classifier', clf)
                ])
                
                # Cross-validation
                scores = cross_val_score(pipeline, X_train, y_train, cv=3, scoring='accuracy')
                avg_score = np.mean(scores)
                
                logger.info(f"{name}: {avg_score:.3f} (+/- {scores.std() * 2:.3f})")
                
                if avg_score > best_score:
                    best_score = avg_score
                    best_classifier = pipeline
            
            # Train best classifier on full training set
            if best_classifier:
                best_classifier.fit(X_train, y_train)
                
                # Evaluate on test set
                y_pred = best_classifier.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                logger.info(f"Test accuracy: {accuracy:.3f}")
                logger.info(f"Classification report:\n{classification_report(y_test, y_pred)}")
                
                # Store the trained model
                self.classifier = best_classifier
                self.vectorizer = best_classifier.named_steps['tfidf']
                
                # Save model
                self._save_model()
                
        except Exception as e:
            logger.error(f"Error training model: {e}")
    
    def classify_document(self, text: str) -> ClassificationResult:
        """Classify a document and return detailed results"""
        try:
            if not self.classifier:
                return ClassificationResult(
                    primary_category="General",
                    secondary_category="Unclassified",
                    confidence=0.5,
                    all_probabilities={"General": 0.5}
                )
            
            # Preprocess text
            processed_text = self._preprocess_text(text)
            
            # Get prediction and probabilities
            prediction = self.classifier.predict([processed_text])[0]
            probabilities = self.classifier.predict_proba([processed_text])[0]
            
            # Create probability dictionary
            classes = self.classifier.classes_
            prob_dict = dict(zip(classes, probabilities))
            
            # Get confidence (highest probability)
            confidence = max(probabilities)
            
            # Determine secondary category
            secondary_category = self._determine_secondary_category(text, prediction)
            
            return ClassificationResult(
                primary_category=prediction,
                secondary_category=secondary_category,
                confidence=confidence,
                all_probabilities=prob_dict
            )
            
        except Exception as e:
            logger.error(f"Error classifying document: {e}")
            return ClassificationResult(
                primary_category="General",
                secondary_category="Unclassified",
                confidence=0.5,
                all_probabilities={"General": 0.5}
            )
    
    def _determine_secondary_category(self, text: str, primary_category: str) -> Optional[str]:
        """Determine secondary category based on primary category"""
        if primary_category not in self.classification_tree:
            return None
        
        subcategories = self.classification_tree[primary_category]['subcategories']
        text_lower = text.lower()
        
        # Simple keyword-based secondary classification
        subcategory_scores = {}
        
        for subcategory in subcategories:
            score = 0
            subcategory_keywords = self._get_subcategory_keywords(primary_category, subcategory)
            
            for keyword in subcategory_keywords:
                score += text_lower.count(keyword.lower())
            
            subcategory_scores[subcategory] = score
        
        # Return subcategory with highest score
        if subcategory_scores:
            best_subcategory = max(subcategory_scores, key=subcategory_scores.get)
            if subcategory_scores[best_subcategory] > 0:
                return best_subcategory
        
        return subcategories[0] if subcategories else None  # Default to first subcategory
    
    def _get_subcategory_keywords(self, primary_category: str, subcategory: str) -> List[str]:
        """Get keywords for a specific subcategory"""
        # This is a simplified implementation
        # In a real system, you would have specific keywords for each subcategory
        keyword_map = {
            'Research Paper': ['research', 'study', 'paper', 'findings'],
            'Thesis': ['thesis', 'dissertation', 'graduate', 'phd'],
            'Financial Report': ['financial', 'report', 'quarterly', 'annual'],
            'Business Plan': ['business', 'plan', 'strategy', 'goals'],
            'Software Documentation': ['software', 'documentation', 'code', 'programming'],
            'API Reference': ['api', 'reference', 'endpoint', 'method'],
            'Contract': ['contract', 'agreement', 'terms', 'conditions'],
            'Legal Brief': ['brief', 'court', 'case', 'legal'],
            'Clinical Study': ['clinical', 'study', 'trial', 'patient'],
            'Medical Report': ['medical', 'report', 'diagnosis', 'treatment'],
            # Add more mappings as needed
        }
        
        return keyword_map.get(subcategory, [])
    
    def _preprocess_text(self, text: str) -> str:
        """Preprocess text for classification"""
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters but keep spaces
        text = re.sub(r'[^a-zA-Z\s]', ' ', text)
        
        # Tokenize
        tokens = word_tokenize(text)
        
        # Remove stopwords and stem
        processed_tokens = []
        for token in tokens:
            if token not in self.stop_words and len(token) > 2:
                stemmed = self.stemmer.stem(token)
                processed_tokens.append(stemmed)
        
        return ' '.join(processed_tokens)
    
    def _save_model(self):
        """Save the trained model"""
        try:
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            
            model_data = {
                'classifier': self.classifier,
                'vectorizer': self.vectorizer,
                'classification_tree': self.classification_tree
            }
            
            with open(self.model_path, 'wb') as f:
                pickle.dump(model_data, f)
            
            logger.info(f"Model saved to {self.model_path}")
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
    
    def get_classification_tree(self) -> Dict:
        """Get the classification tree structure"""
        return self.classification_tree
    
    def get_category_statistics(self, documents: List[Dict]) -> Dict:
        """Get statistics about document categories"""
        stats = {}
        
        for category in self.primary_categories:
            stats[category] = {
                'count': 0,
                'subcategories': {}
            }
        
        for doc in documents:
            primary = doc.get('classification', 'General')
            secondary = doc.get('secondary_classification')
            
            if primary in stats:
                stats[primary]['count'] += 1
                
                if secondary:
                    if secondary not in stats[primary]['subcategories']:
                        stats[primary]['subcategories'][secondary] = 0
                    stats[primary]['subcategories'][secondary] += 1
        
        return stats
    
    def retrain_with_feedback(self, documents: List[Dict], user_corrections: List[Dict]):
        """Retrain model with user feedback"""
        try:
            # Combine original training data with user corrections
            texts = []
            labels = []
            
            for doc in documents:
                if doc.get('content_text') and doc.get('classification'):
                    texts.append(doc['content_text'])
                    labels.append(doc['classification'])
            
            for correction in user_corrections:
                if correction.get('text') and correction.get('correct_label'):
                    texts.append(correction['text'])
                    labels.append(correction['correct_label'])
            
            if len(texts) > 10:  # Minimum data for retraining
                self.train_model(texts, labels)
                logger.info(f"Model retrained with {len(user_corrections)} corrections")
            
        except Exception as e:
            logger.error(f"Error retraining model: {e}")
