import os
import time
import logging
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from src.models.document import db, Document, SearchLog
from src.utils.document_processor import DocumentProcessor
from src.services.cloud_storage import CloudStorageFactory
from src.services.search_service import SearchQuery
from datetime import datetime

logger = logging.getLogger(__name__)

document_bp = Blueprint('document', __name__)
processor = DocumentProcessor()

UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'pdf', 'docx', 'doc', 'txt'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_folder():
    upload_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), UPLOAD_FOLDER)
    if not os.path.exists(upload_path):
        os.makedirs(upload_path)
    return upload_path

@document_bp.route('/upload', methods=['POST'])
def upload_document():
    """Upload and process a document with cloud storage support"""
    start_time = time.time()

    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if not allowed_file(file.filename):
            return jsonify({'error': f'File type not allowed. Supported types: {", ".join(ALLOWED_EXTENSIONS)}'}), 400

        # Get cloud storage configuration
        use_cloud_storage = current_app.config.get('USE_CLOUD_STORAGE', False)
        cloud_provider = current_app.config.get('CLOUD_PROVIDER', 'local')

        # Save file locally first
        upload_path = ensure_upload_folder()
        filename = secure_filename(file.filename)
        local_file_path = os.path.join(upload_path, filename)
        file.save(local_file_path)

        # Upload to cloud storage if configured
        cloud_url = None
        if use_cloud_storage and cloud_provider != 'local':
            try:
                storage_service = CloudStorageFactory.create_storage(
                    provider=cloud_provider,
                    bucket_name=current_app.config.get('CLOUD_BUCKET'),
                    region=current_app.config.get('AWS_REGION'),
                    project_id=current_app.config.get('GOOGLE_CLOUD_PROJECT')
                )

                with open(local_file_path, 'rb') as f:
                    cloud_url = storage_service.upload_file(f, filename, 'documents')

                logger.info(f"File uploaded to cloud storage: {cloud_url}")

            except Exception as e:
                logger.error(f"Cloud storage upload failed: {e}")
                # Continue with local storage

        # Process the document
        file_extension = filename.rsplit('.', 1)[1].lower()

        try:
            if file_extension == 'pdf':
                title = processor.extract_title_from_pdf(local_file_path)
                content_text = processor.extract_text_from_pdf(local_file_path)
                metadata = processor.extract_metadata_from_pdf(local_file_path)
            elif file_extension in ['docx', 'doc']:
                title = processor.extract_title_from_docx(local_file_path)
                content_text = processor.extract_text_from_docx(local_file_path)
                metadata = processor.extract_metadata_from_docx(local_file_path)
            else:  # txt or other
                title = os.path.splitext(filename)[0]
                with open(local_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content_text = f.read()
                metadata = {}

        except Exception as e:
            logger.error(f"Error processing document content: {e}")
            title = os.path.splitext(filename)[0]
            content_text = f"Error processing content: {str(e)}"
            metadata = {}

        # Classify the document using the new classification service
        try:
            classification_result = current_app.classification_service.classify_document(content_text)
            classification = classification_result.primary_category
            confidence = classification_result.confidence
            secondary_classification = classification_result.secondary_category
        except Exception as e:
            logger.error(f"Error classifying document: {e}")
            classification = "General"
            confidence = 0.5
            secondary_classification = None

        # Get file size
        file_size = os.path.getsize(local_file_path)

        # Create document record
        document = Document(
            title=title,
            filename=filename,
            file_path=cloud_url or local_file_path,
            file_size=file_size,
            content_text=content_text,
            classification=classification,
            classification_confidence=confidence,
            author=metadata.get('author'),
            creation_date=metadata.get('creation_date'),
            last_modified=metadata.get('last_modified')
        )

        # Add secondary classification if available
        if hasattr(document, 'secondary_classification'):
            document.secondary_classification = secondary_classification

        db.session.add(document)
        db.session.commit()

        # Update search index
        try:
            documents = Document.query.all()
            doc_data = [doc.to_dict() for doc in documents]
            current_app.search_service.index_documents(doc_data)
        except Exception as e:
            logger.error(f"Error updating search index: {e}")

        # Log performance
        processing_time = time.time() - start_time
        current_app.analytics_service.log_performance(
            'document_upload', processing_time, 1, True
        )

        return jsonify({
            'message': 'Document uploaded and processed successfully',
            'document': document.to_dict(),
            'processing_time': round(processing_time, 3),
            'cloud_storage_used': cloud_url is not None
        }), 201

    except Exception as e:
        processing_time = time.time() - start_time
        current_app.analytics_service.log_performance(
            'document_upload', processing_time, 1, False, str(e)
        )
        logger.error(f"Error processing document: {e}")
        return jsonify({'error': f'Error processing document: {str(e)}'}), 500

@document_bp.route('/documents', methods=['GET'])
def get_documents():
    """Get all documents with optional sorting"""
    try:
        sort_by = request.args.get('sort_by', 'upload_date')
        sort_order = request.args.get('sort_order', 'desc')
        
        # Measure sorting time
        start_time = time.time()
        
        query = Document.query
        
        if sort_by == 'title':
            if sort_order == 'asc':
                query = query.order_by(Document.title.asc())
            else:
                query = query.order_by(Document.title.desc())
        elif sort_by == 'upload_date':
            if sort_order == 'asc':
                query = query.order_by(Document.upload_date.asc())
            else:
                query = query.order_by(Document.upload_date.desc())
        elif sort_by == 'file_size':
            if sort_order == 'asc':
                query = query.order_by(Document.file_size.asc())
            else:
                query = query.order_by(Document.file_size.desc())
        
        documents = query.all()
        sort_time = time.time() - start_time
        
        return jsonify({
            'documents': [doc.to_dict() for doc in documents],
            'sort_time': sort_time,
            'total_count': len(documents)
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Error retrieving documents: {str(e)}'}), 500

@document_bp.route('/search', methods=['POST'])
def search_documents():
    """Advanced search documents with highlighting and ranking"""
    try:
        data = request.get_json()
        keywords = data.get('keywords', '').strip()

        if not keywords:
            return jsonify({'error': 'Keywords are required'}), 400

        # Parse search parameters
        exact_phrase = data.get('exact_phrase', False)
        case_sensitive = data.get('case_sensitive', False)
        include_title = data.get('include_title', True)
        include_content = data.get('include_content', True)
        classification_filter = data.get('classification_filter')
        limit = data.get('limit', 20)

        # Create search query
        search_query = SearchQuery(
            keywords=keywords,
            exact_phrase=exact_phrase,
            case_sensitive=case_sensitive,
            include_title=include_title,
            include_content=include_content,
            classification_filter=classification_filter
        )

        # Measure search time
        start_time = time.time()

        # Perform advanced search
        search_results = current_app.search_service.search(search_query, limit)

        search_time = time.time() - start_time

        # Convert search results to response format
        response_documents = []
        for result in search_results:
            response_documents.append({
                'id': result.document_id,
                'title': result.title,
                'filename': result.filename,
                'relevance_score': result.relevance_score,
                'highlighted_content': result.highlighted_content,
                'snippet': result.snippet,
                'match_count': result.match_count,
                'classification': result.classification
            })

        # Log the search
        search_log = SearchLog(
            query=keywords,
            results_count=len(search_results),
            search_time=search_time
        )
        db.session.add(search_log)
        db.session.commit()

        # Log performance
        current_app.analytics_service.log_performance(
            'document_search', search_time, len(search_results), True
        )

        return jsonify({
            'documents': response_documents,
            'search_time': search_time,
            'results_count': len(search_results),
            'query': keywords,
            'search_parameters': {
                'exact_phrase': exact_phrase,
                'case_sensitive': case_sensitive,
                'classification_filter': classification_filter
            }
        }), 200

    except Exception as e:
        search_time = time.time() - start_time if 'start_time' in locals() else 0
        current_app.analytics_service.log_performance(
            'document_search', search_time, 0, False, str(e)
        )
        logger.error(f"Error searching documents: {e}")
        return jsonify({'error': f'Error searching documents: {str(e)}'}), 500

@document_bp.route('/search/suggestions', methods=['GET'])
def get_search_suggestions():
    """Get search suggestions based on partial query"""
    try:
        partial_query = request.args.get('q', '').strip()
        limit = int(request.args.get('limit', 5))

        if len(partial_query) < 2:
            return jsonify({
                'suggestions': [],
                'query': partial_query
            }), 200

        # Get suggestions from search service
        suggestions = current_app.search_service.get_search_suggestions(partial_query, limit)

        return jsonify({
            'suggestions': suggestions,
            'query': partial_query
        }), 200

    except Exception as e:
        logger.error(f"Error getting search suggestions: {e}")
        return jsonify({'error': f'Error getting search suggestions: {str(e)}'}), 500

@document_bp.route('/classify', methods=['POST'])
def classify_documents():
    """Classify all documents or reclassify existing ones using advanced ML"""
    try:
        start_time = time.time()

        # Get parameters
        data = request.get_json() or {}
        force_reclassify = data.get('force_reclassify', False)
        document_ids = data.get('document_ids')  # Specific documents to classify

        # Get documents to classify
        if document_ids:
            documents = Document.query.filter(Document.id.in_(document_ids)).all()
        else:
            if force_reclassify:
                documents = Document.query.all()
            else:
                # Only classify unclassified documents
                documents = Document.query.filter(
                    (Document.classification.is_(None)) |
                    (Document.classification == '') |
                    (Document.classification == 'General')
                ).all()

        classified_count = 0
        classification_results = []

        for document in documents:
            if document.content_text:
                try:
                    # Use advanced classification service
                    classification_result = current_app.classification_service.classify_document(
                        document.content_text
                    )

                    # Update document
                    old_classification = document.classification
                    document.classification = classification_result.primary_category
                    document.classification_confidence = classification_result.confidence

                    # Add secondary classification if available
                    if hasattr(document, 'secondary_classification'):
                        document.secondary_classification = classification_result.secondary_category

                    classification_results.append({
                        'document_id': document.id,
                        'title': document.title,
                        'old_classification': old_classification,
                        'new_classification': classification_result.primary_category,
                        'secondary_classification': classification_result.secondary_category,
                        'confidence': classification_result.confidence,
                        'all_probabilities': classification_result.all_probabilities
                    })

                    classified_count += 1

                except Exception as e:
                    logger.error(f"Error classifying document {document.id}: {e}")
                    continue

        db.session.commit()
        classification_time = time.time() - start_time

        # Update search index
        try:
            documents = Document.query.all()
            doc_data = [doc.to_dict() for doc in documents]
            current_app.search_service.index_documents(doc_data)
        except Exception as e:
            logger.error(f"Error updating search index: {e}")

        # Log performance
        current_app.analytics_service.log_performance(
            'document_classification', classification_time, classified_count, True
        )

        return jsonify({
            'message': f'Successfully classified {classified_count} documents',
            'classification_time': classification_time,
            'classified_count': classified_count,
            'total_documents': len(documents),
            'classification_results': classification_results[:10]  # Return first 10 for preview
        }), 200

    except Exception as e:
        classification_time = time.time() - start_time if 'start_time' in locals() else 0
        current_app.analytics_service.log_performance(
            'document_classification', classification_time, 0, False, str(e)
        )
        logger.error(f"Error classifying documents: {e}")
        return jsonify({'error': f'Error classifying documents: {str(e)}'}), 500

@document_bp.route('/classification/tree', methods=['GET'])
def get_classification_tree():
    """Get the classification tree structure"""
    try:
        tree = current_app.classification_service.get_classification_tree()

        return jsonify({
            'classification_tree': tree,
            'primary_categories': list(tree.keys())
        }), 200

    except Exception as e:
        logger.error(f"Error getting classification tree: {e}")
        return jsonify({'error': f'Error getting classification tree: {str(e)}'}), 500

@document_bp.route('/classification/retrain', methods=['POST'])
def retrain_classification_model():
    """Retrain classification model with user feedback"""
    try:
        data = request.get_json()

        if not data or 'corrections' not in data:
            return jsonify({'error': 'User corrections are required'}), 400

        corrections = data['corrections']

        # Get all documents for retraining
        documents = Document.query.all()
        doc_data = [doc.to_dict() for doc in documents]

        # Retrain model
        current_app.classification_service.retrain_with_feedback(doc_data, corrections)

        return jsonify({
            'message': 'Classification model retrained successfully',
            'corrections_applied': len(corrections)
        }), 200

    except Exception as e:
        logger.error(f"Error retraining classification model: {e}")
        return jsonify({'error': f'Error retraining model: {str(e)}'}), 500

@document_bp.route('/statistics', methods=['GET'])
def get_statistics():
    """Get system statistics"""
    try:
        # Document statistics
        total_documents = Document.query.count()
        total_size = db.session.query(db.func.sum(Document.file_size)).scalar() or 0
        
        # Classification statistics
        classification_stats = db.session.query(
            Document.classification,
            db.func.count(Document.id)
        ).group_by(Document.classification).all()
        
        # Recent search statistics
        recent_searches = SearchLog.query.order_by(SearchLog.timestamp.desc()).limit(10).all()
        avg_search_time = db.session.query(db.func.avg(SearchLog.search_time)).scalar() or 0
        
        return jsonify({
            'total_documents': total_documents,
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'classification_distribution': {
                category: count for category, count in classification_stats
            },
            'recent_searches': [search.to_dict() for search in recent_searches],
            'average_search_time': round(avg_search_time, 4)
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Error retrieving statistics: {str(e)}'}), 500

@document_bp.route('/document/<int:document_id>', methods=['GET'])
def get_document(document_id):
    """Get a specific document by ID"""
    try:
        document = Document.query.get_or_404(document_id)
        return jsonify({'document': document.to_dict()}), 200
        
    except Exception as e:
        return jsonify({'error': f'Error retrieving document: {str(e)}'}), 500

@document_bp.route('/document/<int:document_id>', methods=['DELETE'])
def delete_document(document_id):
    """Delete a specific document"""
    try:
        document = Document.query.get_or_404(document_id)
        
        # Delete the file from filesystem
        if os.path.exists(document.file_path):
            os.remove(document.file_path)
        
        # Delete from database
        db.session.delete(document)
        db.session.commit()
        
        return jsonify({'message': 'Document deleted successfully'}), 200
        
    except Exception as e:
        return jsonify({'error': f'Error deleting document: {str(e)}'}), 500

