import os
import boto3
from google.cloud import storage as gcs
from azure.storage.blob import BlobServiceClient
from abc import ABC, abstractmethod
import logging
from typing import Optional, BinaryIO
from werkzeug.utils import secure_filename

logger = logging.getLogger(__name__)

class CloudStorageInterface(ABC):
    """Abstract interface for cloud storage providers"""
    
    @abstractmethod
    def upload_file(self, file_obj: BinaryIO, filename: str, folder: str = "") -> str:
        """Upload a file and return the URL"""
        pass
    
    @abstractmethod
    def download_file(self, filename: str, folder: str = "") -> bytes:
        """Download a file and return its content"""
        pass
    
    @abstractmethod
    def delete_file(self, filename: str, folder: str = "") -> bool:
        """Delete a file"""
        pass
    
    @abstractmethod
    def list_files(self, folder: str = "") -> list:
        """List files in a folder"""
        pass
    
    @abstractmethod
    def get_file_url(self, filename: str, folder: str = "") -> str:
        """Get the public URL of a file"""
        pass

class AWSStorageService(CloudStorageInterface):
    """AWS S3 storage service"""
    
    def __init__(self, bucket_name: str, region: str = 'us-east-1'):
        self.bucket_name = bucket_name
        self.region = region
        self.s3_client = boto3.client('s3', region_name=region)
    
    def upload_file(self, file_obj: BinaryIO, filename: str, folder: str = "") -> str:
        try:
            key = f"{folder}/{filename}" if folder else filename
            key = key.lstrip('/')  # Remove leading slash
            
            self.s3_client.upload_fileobj(
                file_obj, 
                self.bucket_name, 
                key,
                ExtraArgs={'ContentType': self._get_content_type(filename)}
            )
            
            return f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{key}"
        except Exception as e:
            logger.error(f"Error uploading to S3: {e}")
            raise
    
    def download_file(self, filename: str, folder: str = "") -> bytes:
        try:
            key = f"{folder}/{filename}" if folder else filename
            key = key.lstrip('/')
            
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=key)
            return response['Body'].read()
        except Exception as e:
            logger.error(f"Error downloading from S3: {e}")
            raise
    
    def delete_file(self, filename: str, folder: str = "") -> bool:
        try:
            key = f"{folder}/{filename}" if folder else filename
            key = key.lstrip('/')
            
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=key)
            return True
        except Exception as e:
            logger.error(f"Error deleting from S3: {e}")
            return False
    
    def list_files(self, folder: str = "") -> list:
        try:
            prefix = f"{folder}/" if folder else ""
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name, 
                Prefix=prefix
            )
            
            files = []
            if 'Contents' in response:
                for obj in response['Contents']:
                    files.append({
                        'name': obj['Key'].replace(prefix, ''),
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'],
                        'url': f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{obj['Key']}"
                    })
            return files
        except Exception as e:
            logger.error(f"Error listing S3 files: {e}")
            return []
    
    def get_file_url(self, filename: str, folder: str = "") -> str:
        key = f"{folder}/{filename}" if folder else filename
        key = key.lstrip('/')
        return f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{key}"
    
    def _get_content_type(self, filename: str) -> str:
        ext = filename.lower().split('.')[-1]
        content_types = {
            'pdf': 'application/pdf',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'doc': 'application/msword',
            'txt': 'text/plain'
        }
        return content_types.get(ext, 'application/octet-stream')

class GoogleCloudStorageService(CloudStorageInterface):
    """Google Cloud Storage service"""
    
    def __init__(self, bucket_name: str, project_id: str):
        self.bucket_name = bucket_name
        self.project_id = project_id
        self.client = gcs.Client(project=project_id)
        self.bucket = self.client.bucket(bucket_name)
    
    def upload_file(self, file_obj: BinaryIO, filename: str, folder: str = "") -> str:
        try:
            blob_name = f"{folder}/{filename}" if folder else filename
            blob_name = blob_name.lstrip('/')
            
            blob = self.bucket.blob(blob_name)
            blob.upload_from_file(file_obj, content_type=self._get_content_type(filename))
            
            return f"https://storage.googleapis.com/{self.bucket_name}/{blob_name}"
        except Exception as e:
            logger.error(f"Error uploading to GCS: {e}")
            raise
    
    def download_file(self, filename: str, folder: str = "") -> bytes:
        try:
            blob_name = f"{folder}/{filename}" if folder else filename
            blob_name = blob_name.lstrip('/')
            
            blob = self.bucket.blob(blob_name)
            return blob.download_as_bytes()
        except Exception as e:
            logger.error(f"Error downloading from GCS: {e}")
            raise
    
    def delete_file(self, filename: str, folder: str = "") -> bool:
        try:
            blob_name = f"{folder}/{filename}" if folder else filename
            blob_name = blob_name.lstrip('/')
            
            blob = self.bucket.blob(blob_name)
            blob.delete()
            return True
        except Exception as e:
            logger.error(f"Error deleting from GCS: {e}")
            return False
    
    def list_files(self, folder: str = "") -> list:
        try:
            prefix = f"{folder}/" if folder else ""
            blobs = self.bucket.list_blobs(prefix=prefix)
            
            files = []
            for blob in blobs:
                files.append({
                    'name': blob.name.replace(prefix, ''),
                    'size': blob.size,
                    'last_modified': blob.time_created,
                    'url': f"https://storage.googleapis.com/{self.bucket_name}/{blob.name}"
                })
            return files
        except Exception as e:
            logger.error(f"Error listing GCS files: {e}")
            return []
    
    def get_file_url(self, filename: str, folder: str = "") -> str:
        blob_name = f"{folder}/{filename}" if folder else filename
        blob_name = blob_name.lstrip('/')
        return f"https://storage.googleapis.com/{self.bucket_name}/{blob_name}"
    
    def _get_content_type(self, filename: str) -> str:
        ext = filename.lower().split('.')[-1]
        content_types = {
            'pdf': 'application/pdf',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'doc': 'application/msword',
            'txt': 'text/plain'
        }
        return content_types.get(ext, 'application/octet-stream')

class LocalStorageService(CloudStorageInterface):
    """Local file system storage service (fallback)"""
    
    def __init__(self, base_path: str = "uploads"):
        self.base_path = base_path
        os.makedirs(base_path, exist_ok=True)
    
    def upload_file(self, file_obj: BinaryIO, filename: str, folder: str = "") -> str:
        try:
            folder_path = os.path.join(self.base_path, folder) if folder else self.base_path
            os.makedirs(folder_path, exist_ok=True)
            
            file_path = os.path.join(folder_path, secure_filename(filename))
            
            with open(file_path, 'wb') as f:
                f.write(file_obj.read())
            
            return file_path
        except Exception as e:
            logger.error(f"Error uploading to local storage: {e}")
            raise
    
    def download_file(self, filename: str, folder: str = "") -> bytes:
        try:
            folder_path = os.path.join(self.base_path, folder) if folder else self.base_path
            file_path = os.path.join(folder_path, filename)
            
            with open(file_path, 'rb') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error downloading from local storage: {e}")
            raise
    
    def delete_file(self, filename: str, folder: str = "") -> bool:
        try:
            folder_path = os.path.join(self.base_path, folder) if folder else self.base_path
            file_path = os.path.join(folder_path, filename)
            
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting from local storage: {e}")
            return False
    
    def list_files(self, folder: str = "") -> list:
        try:
            folder_path = os.path.join(self.base_path, folder) if folder else self.base_path
            
            if not os.path.exists(folder_path):
                return []
            
            files = []
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                if os.path.isfile(file_path):
                    stat = os.stat(file_path)
                    files.append({
                        'name': filename,
                        'size': stat.st_size,
                        'last_modified': stat.st_mtime,
                        'url': file_path
                    })
            return files
        except Exception as e:
            logger.error(f"Error listing local files: {e}")
            return []
    
    def get_file_url(self, filename: str, folder: str = "") -> str:
        folder_path = os.path.join(self.base_path, folder) if folder else self.base_path
        return os.path.join(folder_path, filename)

class CloudStorageFactory:
    """Factory class to create cloud storage instances"""
    
    @staticmethod
    def create_storage(provider: str = "local", **kwargs) -> CloudStorageInterface:
        if provider.lower() == "aws":
            return AWSStorageService(
                bucket_name=kwargs.get('bucket_name'),
                region=kwargs.get('region', 'us-east-1')
            )
        elif provider.lower() == "gcp":
            return GoogleCloudStorageService(
                bucket_name=kwargs.get('bucket_name'),
                project_id=kwargs.get('project_id')
            )
        elif provider.lower() == "local":
            return LocalStorageService(
                base_path=kwargs.get('base_path', 'uploads')
            )
        else:
            raise ValueError(f"Unsupported storage provider: {provider}")
