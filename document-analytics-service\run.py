#!/usr/bin/env python3
"""
Document Analytics Service - Main Application Runner
A comprehensive cloud-based document analytics platform for searching, sorting, and classifying documents.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.main import create_app

def setup_directories():
    """Create necessary directories if they don't exist"""
    directories = [
        'logs',
        'uploads',
        'models',
        'src/database'
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(exist_ok=True)
        print(f"✓ Directory created/verified: {directory}")

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'flask',
        'flask_cors',
        'flask_sqlalchemy',
        'PyPDF2',
        'scikit-learn',
        'nltk',
        'requests',
        'beautifulsoup4'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nPlease install missing packages using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✓ All required dependencies are installed")
    return True

def download_nltk_data():
    """Download required NLTK data"""
    try:
        import nltk
        
        nltk_data = [
            'punkt',
            'stopwords',
            'averaged_perceptron_tagger',
            'wordnet'
        ]
        
        for data in nltk_data:
            try:
                nltk.data.find(f'tokenizers/{data}' if data == 'punkt' else f'corpora/{data}' if data in ['stopwords', 'wordnet'] else f'taggers/{data}')
            except LookupError:
                print(f"Downloading NLTK data: {data}")
                nltk.download(data, quiet=True)
        
        print("✓ NLTK data verified/downloaded")
        return True
        
    except Exception as e:
        print(f"❌ Error downloading NLTK data: {e}")
        return False

def main():
    """Main application entry point"""
    print("🚀 Starting Document Analytics Service...")
    print("=" * 50)
    
    # Setup directories
    setup_directories()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Download NLTK data
    if not download_nltk_data():
        print("⚠️  Warning: NLTK data download failed. Some features may not work properly.")
    
    # Get configuration
    config_name = os.environ.get('FLASK_ENV', 'development')
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '0.0.0.0')
    debug = os.environ.get('DEBUG', 'True').lower() == 'true'
    
    print(f"✓ Configuration: {config_name}")
    print(f"✓ Server will run on: http://{host}:{port}")
    
    # Create and run the app
    try:
        app = create_app(config_name)
        
        print("\n🎉 Document Analytics Service is ready!")
        print("=" * 50)
        print("Available endpoints:")
        print("  📄 Document Upload: POST /api/upload")
        print("  🔍 Document Search: POST /api/search")
        print("  📊 Analytics: GET /api/analytics/comprehensive")
        print("  🌐 Web Scraping: POST /api/scraping/scrape-url")
        print("  🏷️  Classification: POST /api/classify")
        print("=" * 50)
        
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
        
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
