import os
import re
import PyPDF2
import pdfplumber
from docx import Document as DocxDocument
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.ensemble import RandomForestClassifier
from sklearn.pipeline import Pipeline
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score
import pickle
import nltk
from datetime import datetime
import logging
from typing import Dict, List, Tuple, Optional
import json

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

try:
    nltk.data.find('taggers/averaged_perceptron_tagger')
except LookupError:
    nltk.download('averaged_perceptron_tagger')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

class DocumentProcessor:
    def __init__(self):
        self.classifier = None
        self.categories = [
            'Academic', 'Business', 'Technical', 'Legal', 'Medical',
            'Financial', 'Research', 'Educational', 'Government', 'General'
        ]
        self.classification_tree = {
            'Academic': ['Research', 'Educational', 'Scientific'],
            'Business': ['Financial', 'Marketing', 'Management', 'Strategy'],
            'Technical': ['Software', 'Engineering', 'IT', 'Development'],
            'Legal': ['Contract', 'Regulation', 'Compliance', 'Court'],
            'Medical': ['Clinical', 'Pharmaceutical', 'Healthcare', 'Research'],
            'Financial': ['Banking', 'Investment', 'Accounting', 'Insurance'],
            'Research': ['Academic', 'Scientific', 'Market', 'Survey'],
            'Educational': ['Training', 'Curriculum', 'Academic', 'Learning'],
            'Government': ['Policy', 'Regulation', 'Public', 'Administrative'],
            'General': ['Miscellaneous', 'Other', 'Unclassified']
        }
        self._initialize_classifier()

    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        if not text:
            return ""

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)]', ' ', text)

        # Remove excessive newlines
        text = re.sub(r'\n+', '\n', text)

        return text.strip()

    def _extract_title_from_text(self, text: str) -> Optional[str]:
        """Extract title from text using heuristics"""
        if not text:
            return None

        lines = text.split('\n')

        # Look for title patterns in the first 15 lines
        for i, line in enumerate(lines[:15]):
            line = line.strip()

            # Skip empty lines
            if not line:
                continue

            # Skip lines that look like headers/footers
            if any(keyword in line.lower() for keyword in ['page', 'copyright', '©', 'confidential']):
                continue

            # Check if line looks like a title
            if self._is_likely_title(line):
                return line

        return None

    def _is_likely_title(self, line: str) -> bool:
        """Determine if a line is likely to be a title"""
        line = line.strip()

        # Basic length checks
        if len(line) < 5 or len(line) > 200:
            return False

        # Check for title characteristics
        word_count = len(line.split())
        if word_count < 2 or word_count > 20:
            return False

        # Titles often have title case or are all caps
        if line.istitle() or line.isupper():
            return True

        # Check for common title patterns
        title_patterns = [
            r'^[A-Z][a-z].*[A-Z].*',  # Mixed case starting with capital
            r'^[A-Z\s]+$',  # All caps with spaces
            r'.*:\s*[A-Z].*',  # Contains colon followed by capital letter
        ]

        for pattern in title_patterns:
            if re.match(pattern, line):
                return True

        return False
    
    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text content from PDF file using multiple methods for better accuracy"""
        try:
            # Try pdfplumber first (better for complex layouts)
            try:
                with pdfplumber.open(file_path) as pdf:
                    text = ""
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                    if text.strip():
                        return self._clean_text(text)
            except Exception as e:
                logger.warning(f"pdfplumber failed for {file_path}: {e}")

            # Fallback to PyPDF2
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
                return self._clean_text(text)

        except Exception as e:
            logger.error(f"Error extracting text from PDF {file_path}: {e}")
            return ""
    
    def extract_text_from_docx(self, file_path: str) -> str:
        """Extract text content from DOCX file"""
        try:
            doc = DocxDocument(file_path)
            text = ""

            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text += paragraph.text + "\n"

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text += cell.text + " "
                    text += "\n"

            return self._clean_text(text)

        except Exception as e:
            logger.error(f"Error extracting text from DOCX {file_path}: {e}")
            return f"DOCX file: {os.path.basename(file_path)}"
    
    def extract_title_from_pdf(self, file_path: str) -> str:
        """Extract title from PDF metadata or content using intelligent heuristics"""
        try:
            # Try to get title from metadata first
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)

                if pdf_reader.metadata and pdf_reader.metadata.title:
                    title = pdf_reader.metadata.title.strip()
                    if len(title) > 3 and len(title) < 200:
                        return title

            # Try to extract title from first page content using pdfplumber
            try:
                with pdfplumber.open(file_path) as pdf:
                    if pdf.pages:
                        first_page_text = pdf.pages[0].extract_text()
                        if first_page_text:
                            title = self._extract_title_from_text(first_page_text)
                            if title:
                                return title
            except Exception as e:
                logger.warning(f"pdfplumber title extraction failed: {e}")

            # Fallback to PyPDF2 for title extraction
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                if len(pdf_reader.pages) > 0:
                    first_page_text = pdf_reader.pages[0].extract_text()
                    title = self._extract_title_from_text(first_page_text)
                    if title:
                        return title

            # Final fallback to filename
            return os.path.splitext(os.path.basename(file_path))[0]

        except Exception as e:
            logger.error(f"Error extracting title from PDF {file_path}: {e}")
            return os.path.splitext(os.path.basename(file_path))[0]
    
    def extract_title_from_docx(self, file_path: str) -> str:
        """Extract title from DOCX file using document properties and content"""
        try:
            doc = DocxDocument(file_path)

            # Try to get title from document properties
            if hasattr(doc.core_properties, 'title') and doc.core_properties.title:
                title = doc.core_properties.title.strip()
                if len(title) > 3 and len(title) < 200:
                    return title

            # Try to extract title from first few paragraphs
            for i, paragraph in enumerate(doc.paragraphs[:10]):
                if paragraph.text.strip():
                    text = paragraph.text.strip()
                    if self._is_likely_title(text):
                        return text

            # Fallback to filename
            return os.path.splitext(os.path.basename(file_path))[0]

        except Exception as e:
            logger.error(f"Error extracting title from DOCX {file_path}: {e}")
            return os.path.splitext(os.path.basename(file_path))[0]
    
    def extract_metadata_from_pdf(self, file_path):
        """Extract metadata from PDF file"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                metadata = {}
                
                if pdf_reader.metadata:
                    metadata['author'] = pdf_reader.metadata.author if pdf_reader.metadata.author else None
                    metadata['creation_date'] = pdf_reader.metadata.creation_date if pdf_reader.metadata.creation_date else None
                    metadata['last_modified'] = pdf_reader.metadata.modification_date if pdf_reader.metadata.modification_date else None
                
                return metadata
        except Exception as e:
            print(f"Error extracting metadata from PDF: {e}")
            return {}
    
    def extract_metadata_from_docx(self, file_path: str) -> Dict:
        """Extract comprehensive metadata from DOCX file"""
        try:
            doc = DocxDocument(file_path)
            metadata = {}

            # Extract core properties
            core_props = doc.core_properties
            metadata['author'] = core_props.author if core_props.author else None
            metadata['title'] = core_props.title if core_props.title else None
            metadata['subject'] = core_props.subject if core_props.subject else None
            metadata['keywords'] = core_props.keywords if core_props.keywords else None
            metadata['creation_date'] = core_props.created if core_props.created else None
            metadata['last_modified'] = core_props.modified if core_props.modified else None
            metadata['last_modified_by'] = core_props.last_modified_by if core_props.last_modified_by else None
            metadata['revision'] = core_props.revision if core_props.revision else None

            # Get file system metadata as fallback
            stat = os.stat(file_path)
            if not metadata['creation_date']:
                metadata['creation_date'] = datetime.fromtimestamp(stat.st_birthtime if hasattr(stat, 'st_birthtime') else stat.st_ctime)
            if not metadata['last_modified']:
                metadata['last_modified'] = datetime.fromtimestamp(stat.st_mtime)

            # Document statistics
            metadata['paragraph_count'] = len(doc.paragraphs)
            metadata['table_count'] = len(doc.tables)

            return metadata

        except Exception as e:
            logger.error(f"Error extracting metadata from DOCX {file_path}: {e}")
            # Fallback to basic file metadata
            try:
                stat = os.stat(file_path)
                return {
                    'author': None,
                    'creation_date': datetime.fromtimestamp(stat.st_birthtime if hasattr(stat, 'st_birthtime') else stat.st_ctime),
                    'last_modified': datetime.fromtimestamp(stat.st_mtime)
                }
            except:
                return {}
    
    def _initialize_classifier(self):
        """Initialize the document classifier with sample training data"""
        # Sample training data for different categories
        training_data = [
            ("research methodology analysis statistical significant", "Academic"),
            ("university college student education learning", "Academic"),
            ("business strategy market revenue profit", "Business"),
            ("company management financial report quarterly", "Business"),
            ("algorithm software programming code development", "Technical"),
            ("system architecture database network security", "Technical"),
            ("contract agreement legal terms conditions", "Legal"),
            ("court case law regulation compliance", "Legal"),
            ("medical patient treatment diagnosis therapy", "Medical"),
            ("health clinical study pharmaceutical drug", "Medical"),
            ("general information document text content", "General"),
            ("various topics discussion overview summary", "General")
        ]
        
        texts = [item[0] for item in training_data]
        labels = [item[1] for item in training_data]
        
        # Create and train the classifier
        self.classifier = Pipeline([
            ('tfidf', TfidfVectorizer(stop_words='english', max_features=1000)),
            ('classifier', MultinomialNB())
        ])
        
        self.classifier.fit(texts, labels)
    
    def classify_document(self, text):
        """Classify document based on its content"""
        if not text or not self.classifier:
            return "General", 0.5
        
        try:
            # Clean and preprocess text
            cleaned_text = re.sub(r'[^a-zA-Z\s]', '', text.lower())
            
            # Predict category and confidence
            prediction = self.classifier.predict([cleaned_text])[0]
            probabilities = self.classifier.predict_proba([cleaned_text])[0]
            confidence = max(probabilities)
            
            return prediction, confidence
        except Exception as e:
            print(f"Error classifying document: {e}")
            return "General", 0.5
    
    def highlight_text(self, text, keywords):
        """Highlight keywords in text"""
        if not keywords or not text:
            return text
        
        # Create a pattern that matches any of the keywords (case-insensitive)
        pattern = '|'.join(re.escape(keyword) for keyword in keywords)
        
        def replace_func(match):
            return f"<mark>{match.group()}</mark>"
        
        highlighted_text = re.sub(pattern, replace_func, text, flags=re.IGNORECASE)
        return highlighted_text
    
    def search_documents(self, documents, keywords):
        """Search documents for keywords and return matching documents"""
        if not keywords:
            return documents
        
        matching_docs = []
        keyword_list = [kw.strip().lower() for kw in keywords.split() if kw.strip()]
        
        for doc in documents:
            content = (doc.get('content_text', '') + ' ' + doc.get('title', '')).lower()
            
            # Check if any keyword is found in the document
            if any(keyword in content for keyword in keyword_list):
                # Highlight the keywords in the content
                highlighted_content = self.highlight_text(
                    doc.get('content_text', ''), 
                    keyword_list
                )
                doc_copy = doc.copy()
                doc_copy['highlighted_content'] = highlighted_content
                matching_docs.append(doc_copy)
        
        return matching_docs

