version: '3.8'

services:
  # Backend API Service
  backend:
    build: 
      context: ./document-analytics-service
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=sqlite:///app.db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-production-secret-key-change-this
    volumes:
      - ./data/uploads:/app/uploads
      - ./data/database:/app/src/database
      - ./data/logs:/app/logs
      - ./data/models:/app/models
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - document-analytics

  # Frontend Service
  frontend:
    build:
      context: ./document-analytics-frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:5000/api
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - document-analytics

  # Redis for caching and task queue
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - document-analytics

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - document-analytics

volumes:
  redis_data:
  postgres_data:

networks:
  document-analytics:
    driver: bridge
