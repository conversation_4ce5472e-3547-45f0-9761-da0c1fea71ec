import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, quote
import time
import logging
from typing import List, Dict, Optional, Set
import os
import tempfile
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import re
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ScrapedDocument:
    """Data class for scraped document information"""
    url: str
    title: str
    filename: str
    content_type: str
    size: Optional[int] = None
    source_page: Optional[str] = None

class WebScraper:
    """Web scraper for collecting documents from various sources"""
    
    def __init__(self, user_agent: str = None, delay: float = 1.0):
        self.user_agent = user_agent or "DocumentAnalytics/1.0 (+https://your-domain.com/bot)"
        self.delay = delay
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        self.visited_urls: Set[str] = set()
        
    def scrape_documents_from_url(self, base_url: str, max_pages: int = 10, 
                                 file_extensions: List[str] = None) -> List[ScrapedDocument]:
        """Scrape documents from a website"""
        if file_extensions is None:
            file_extensions = ['pdf', 'docx', 'doc', 'txt']
        
        documents = []
        urls_to_visit = [base_url]
        visited_count = 0
        
        while urls_to_visit and visited_count < max_pages:
            current_url = urls_to_visit.pop(0)
            
            if current_url in self.visited_urls:
                continue
                
            try:
                logger.info(f"Scraping: {current_url}")
                self.visited_urls.add(current_url)
                visited_count += 1
                
                # Get page content
                response = self.session.get(current_url, timeout=30)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Find document links
                doc_links = self._find_document_links(soup, current_url, file_extensions)
                documents.extend(doc_links)
                
                # Find additional pages to crawl (limited depth)
                if visited_count < max_pages:
                    page_links = self._find_page_links(soup, current_url, base_url)
                    urls_to_visit.extend(page_links[:5])  # Limit new URLs per page
                
                # Respect rate limiting
                time.sleep(self.delay)
                
            except Exception as e:
                logger.error(f"Error scraping {current_url}: {e}")
                continue
        
        return documents
    
    def _find_document_links(self, soup: BeautifulSoup, base_url: str, 
                           file_extensions: List[str]) -> List[ScrapedDocument]:
        """Find document links on a page"""
        documents = []
        
        # Find all links
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link['href']
            absolute_url = urljoin(base_url, href)
            
            # Check if link points to a document
            if self._is_document_url(absolute_url, file_extensions):
                title = self._extract_link_title(link)
                filename = self._extract_filename_from_url(absolute_url)
                content_type = self._get_content_type_from_extension(filename)
                
                doc = ScrapedDocument(
                    url=absolute_url,
                    title=title,
                    filename=filename,
                    content_type=content_type,
                    source_page=base_url
                )
                documents.append(doc)
        
        return documents
    
    def _find_page_links(self, soup: BeautifulSoup, current_url: str, 
                        base_domain: str) -> List[str]:
        """Find additional pages to crawl"""
        page_links = []
        base_domain_parsed = urlparse(base_domain)
        
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link['href']
            absolute_url = urljoin(current_url, href)
            parsed_url = urlparse(absolute_url)
            
            # Only follow links within the same domain
            if (parsed_url.netloc == base_domain_parsed.netloc and 
                absolute_url not in self.visited_urls and
                not self._is_document_url(absolute_url, ['pdf', 'docx', 'doc', 'txt'])):
                page_links.append(absolute_url)
        
        return list(set(page_links))  # Remove duplicates
    
    def _is_document_url(self, url: str, file_extensions: List[str]) -> bool:
        """Check if URL points to a document"""
        parsed_url = urlparse(url)
        path = parsed_url.path.lower()
        
        for ext in file_extensions:
            if path.endswith(f'.{ext.lower()}'):
                return True
        
        return False
    
    def _extract_link_title(self, link) -> str:
        """Extract title from link element"""
        # Try different attributes for title
        title = link.get('title', '').strip()
        if title:
            return title
        
        # Use link text
        text = link.get_text().strip()
        if text:
            return text
        
        # Use href as fallback
        href = link.get('href', '')
        return self._extract_filename_from_url(href)
    
    def _extract_filename_from_url(self, url: str) -> str:
        """Extract filename from URL"""
        parsed_url = urlparse(url)
        path = parsed_url.path
        
        if path:
            filename = os.path.basename(path)
            if filename:
                return filename
        
        # Generate filename from URL
        return f"document_{hash(url) % 10000}.pdf"
    
    def _get_content_type_from_extension(self, filename: str) -> str:
        """Get content type from file extension"""
        ext = filename.lower().split('.')[-1]
        content_types = {
            'pdf': 'application/pdf',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'doc': 'application/msword',
            'txt': 'text/plain'
        }
        return content_types.get(ext, 'application/octet-stream')
    
    def download_document(self, doc: ScrapedDocument, download_path: str) -> Optional[str]:
        """Download a document and return the local file path"""
        try:
            logger.info(f"Downloading: {doc.url}")
            
            response = self.session.get(doc.url, timeout=60, stream=True)
            response.raise_for_status()
            
            # Create download directory if it doesn't exist
            os.makedirs(download_path, exist_ok=True)
            
            # Generate safe filename
            safe_filename = self._make_safe_filename(doc.filename)
            file_path = os.path.join(download_path, safe_filename)
            
            # Download file
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            # Update document size
            doc.size = os.path.getsize(file_path)
            
            logger.info(f"Downloaded: {file_path} ({doc.size} bytes)")
            return file_path
            
        except Exception as e:
            logger.error(f"Error downloading {doc.url}: {e}")
            return None
    
    def _make_safe_filename(self, filename: str) -> str:
        """Make filename safe for filesystem"""
        # Remove or replace unsafe characters
        safe_filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # Limit length
        if len(safe_filename) > 200:
            name, ext = os.path.splitext(safe_filename)
            safe_filename = name[:200-len(ext)] + ext
        
        return safe_filename
    
    def scrape_academic_sources(self, query: str, max_results: int = 20) -> List[ScrapedDocument]:
        """Scrape documents from academic sources"""
        documents = []
        
        # Example academic sources (you would need to implement specific scrapers)
        academic_sources = [
            f"https://arxiv.org/search/?query={quote(query)}&searchtype=all",
            f"https://scholar.google.com/scholar?q={quote(query)}",
            # Add more academic sources as needed
        ]
        
        for source_url in academic_sources:
            try:
                source_docs = self.scrape_documents_from_url(
                    source_url, 
                    max_pages=5, 
                    file_extensions=['pdf']
                )
                documents.extend(source_docs[:max_results//len(academic_sources)])
            except Exception as e:
                logger.error(f"Error scraping academic source {source_url}: {e}")
        
        return documents[:max_results]
    
    def close(self):
        """Clean up resources"""
        self.session.close()

class SeleniumScraper(WebScraper):
    """Enhanced scraper using Selenium for JavaScript-heavy sites"""
    
    def __init__(self, user_agent: str = None, delay: float = 1.0, headless: bool = True):
        super().__init__(user_agent, delay)
        self.headless = headless
        self.driver = None
        
    def _setup_driver(self):
        """Setup Selenium WebDriver"""
        if self.driver is None:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument(f"--user-agent={self.user_agent}")
            
            self.driver = webdriver.Chrome(options=chrome_options)
    
    def scrape_spa_documents(self, url: str, wait_selector: str = None) -> List[ScrapedDocument]:
        """Scrape documents from Single Page Applications"""
        self._setup_driver()
        documents = []
        
        try:
            self.driver.get(url)
            
            # Wait for content to load
            if wait_selector:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, wait_selector))
                )
            else:
                time.sleep(3)  # Default wait
            
            # Get page source and parse with BeautifulSoup
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            documents = self._find_document_links(soup, url, ['pdf', 'docx', 'doc', 'txt'])
            
        except Exception as e:
            logger.error(f"Error scraping SPA {url}: {e}")
        
        return documents
    
    def close(self):
        """Clean up resources"""
        super().close()
        if self.driver:
            self.driver.quit()
            self.driver = None
