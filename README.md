# 📄 Cloud-Based Document Analytics Platform

A comprehensive cloud-based program for searching, sorting, and classifying large collections of documents using advanced machine learning algorithms and cloud storage integration.

## 🌟 Features

### Core Functionality
- **📤 Document Upload & Processing**: Support for PDF, DOCX, DOC, and TXT files
- **🔍 Advanced Search**: Intelligent search with text highlighting and relevance ranking
- **🏷️ Smart Classification**: Hierarchical document classification using ML algorithms
- **📊 Comprehensive Analytics**: Real-time statistics and performance metrics
- **🌐 Web Scraping**: Automated document collection from web sources
- **☁️ Cloud Storage**: Integration with AWS S3, Google Cloud Storage, and Azure

### Advanced Features
- **🎯 Title Extraction**: Intelligent title extraction from document content
- **📈 Performance Monitoring**: Real-time system health and performance tracking
- **🔄 Auto-Classification**: Automatic document categorization with confidence scores
- **💡 Search Suggestions**: Smart search suggestions based on indexed content
- **📱 Responsive UI**: Modern React-based user interface

## 🏗️ Architecture

```
document-analytics-platform/
├── document-analytics-service/     # Backend Flask API
│   ├── src/
│   │   ├── models/                # Database models
│   │   ├── routes/                # API endpoints
│   │   ├── services/              # Business logic services
│   │   ├── utils/                 # Utility functions
│   │   └── main.py               # Application factory
│   ├── requirements.txt          # Python dependencies
│   └── run.py                   # Application runner
├── document-analytics-frontend/   # Frontend React App
│   ├── src/
│   │   ├── components/           # UI components
│   │   └── App.jsx              # Main application
│   └── package.json             # Node dependencies
└── README.md                    # This file
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- Git

### 1. Clone the Repository
```bash
git clone <repository-url>
cd document-analytics-platform
```

### 2. Backend Setup
```bash
cd document-analytics-service

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy environment configuration
cp .env.example .env

# Run the backend
python run.py
```

### 3. Frontend Setup
```bash
cd document-analytics-frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

### 4. Access the Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api

## 📋 Configuration

### Environment Variables
Copy `.env.example` to `.env` and configure:

```env
# Basic Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key

# Cloud Storage (Optional)
USE_CLOUD_STORAGE=False
CLOUD_PROVIDER=local  # aws, gcp, azure, local

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_S3_BUCKET=your-bucket-name

# Database
DATABASE_URL=sqlite:///app.db
```

## 🔧 API Endpoints

### Document Management
- `POST /api/upload` - Upload and process documents
- `GET /api/documents` - List all documents with sorting
- `DELETE /api/document/{id}` - Delete a document

### Search & Classification
- `POST /api/search` - Advanced document search
- `GET /api/search/suggestions` - Get search suggestions
- `POST /api/classify` - Classify documents
- `GET /api/classification/tree` - Get classification hierarchy

### Web Scraping
- `POST /api/scraping/scrape-url` - Scrape documents from URL
- `POST /api/scraping/scrape-academic` - Scrape academic sources

### Analytics
- `GET /api/analytics/comprehensive` - Complete system analytics
- `GET /api/analytics/performance` - Performance metrics
- `GET /api/statistics` - Basic statistics

## 🏷️ Classification System

The system uses a hierarchical classification tree:

```
Academic
├── Research Paper
├── Thesis
├── Conference Paper
└── Journal Article

Business
├── Financial Report
├── Business Plan
├── Marketing Material
└── Strategy Document

Technical
├── Software Documentation
├── API Reference
├── User Manual
└── Technical Specification

Legal
├── Contract
├── Legal Brief
├── Regulation
└── Policy Document

Medical
├── Clinical Study
├── Medical Report
├── Pharmaceutical
└── Healthcare Policy
```

## 🌐 Web Scraping

The platform supports automated document collection:

1. **URL Scraping**: Extract documents from websites
2. **Academic Sources**: Specialized scrapers for research papers
3. **SPA Support**: JavaScript-heavy sites using Selenium
4. **Rate Limiting**: Respectful scraping with configurable delays

## 📊 Analytics Dashboard

Comprehensive analytics including:

- **Document Statistics**: Count, size, types, growth trends
- **Search Analytics**: Popular terms, success rates, performance
- **Classification Distribution**: Category breakdowns and confidence
- **System Performance**: Response times, throughput, error rates
- **User Activity**: Usage patterns and peak hours

## ☁️ Cloud Deployment

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

### Manual Cloud Deployment

#### AWS EC2
1. Launch EC2 instance
2. Install dependencies
3. Configure environment variables
4. Set up reverse proxy (nginx)
5. Configure SSL certificate

#### Google Cloud Platform
1. Create Compute Engine instance
2. Set up Cloud Storage bucket
3. Configure service account
4. Deploy application

#### Azure
1. Create Virtual Machine
2. Set up Blob Storage
3. Configure managed identity
4. Deploy application

## 🧪 Testing

```bash
# Backend tests
cd document-analytics-service
python -m pytest tests/

# Frontend tests
cd document-analytics-frontend
npm test
```

## 📈 Performance Optimization

- **Caching**: Redis for search results and analytics
- **Database**: Optimized queries with proper indexing
- **File Storage**: Cloud storage for scalability
- **Background Tasks**: Celery for heavy processing
- **CDN**: Static asset delivery optimization

## 🔒 Security Features

- **Input Validation**: Comprehensive file and data validation
- **Rate Limiting**: API endpoint protection
- **CORS Configuration**: Secure cross-origin requests
- **File Type Validation**: Restricted upload types
- **SQL Injection Protection**: Parameterized queries

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the API endpoints

## 🎯 Roadmap

- [ ] Real-time collaboration features
- [ ] Advanced ML models (BERT, GPT)
- [ ] Multi-language support
- [ ] Mobile application
- [ ] Advanced visualization tools
- [ ] Integration with popular cloud services
